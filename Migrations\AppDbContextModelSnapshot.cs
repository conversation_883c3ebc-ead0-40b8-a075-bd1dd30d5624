﻿// <auto-generated />
using System;
using ItAssetsSystem.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace ItAssetsSystem.Migrations
{
    [DbContext(typeof(AppDbContext))]
    partial class AppDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.7")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            // PeriodicTaskSchedule实体 - 完全修复版本
            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Tasks.PeriodicTaskSchedule", b =>
                {
                    b.Property<long>("PeriodicTaskScheduleId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("periodic_task_schedule_id");

                    b.Property<long>("TemplateTaskId")
                        .HasColumnType("bigint")
                        .HasColumnName("template_task_id");

                    b.Property<int>("CreatorUserId")
                        .HasColumnType("int")
                        .HasColumnName("creator_user_id");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(255)")
                        .HasColumnName("name");

                    b.Property<string>("Description")
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<string>("RecurrenceType")
                        .IsRequired()
                        .HasColumnType("varchar(50)")
                        .HasColumnName("recurrence_type");

                    b.Property<int>("RecurrenceInterval")
                        .HasColumnType("int")
                        .HasColumnName("recurrence_interval");

                    b.Property<string>("DaysOfWeek")
                        .HasColumnType("json")
                        .HasColumnName("days_of_week");

                    b.Property<int?>("DayOfMonth")
                        .HasColumnType("int")
                        .HasColumnName("day_of_month");

                    b.Property<string>("WeekOfMonth")
                        .HasColumnType("varchar(20)")
                        .HasColumnName("week_of_month");

                    b.Property<string>("DayOfWeekForMonth")
                        .HasColumnType("varchar(20)")
                        .HasColumnName("day_of_week_for_month");

                    b.Property<int?>("MonthOfYear")
                        .HasColumnType("int")
                        .HasColumnName("month_of_year");

                    b.Property<string>("CronExpression")
                        .HasColumnType("varchar(255)")
                        .HasColumnName("cron_expression");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("start_date");

                    b.Property<string>("EndConditionType")
                        .IsRequired()
                        .HasColumnType("varchar(50)")
                        .HasColumnName("end_condition_type");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("end_date");

                    b.Property<int?>("TotalOccurrences")
                        .HasColumnType("int")
                        .HasColumnName("total_occurrences");

                    b.Property<int>("OccurrencesGenerated")
                        .HasColumnType("int")
                        .HasColumnName("occurrences_generated");

                    b.Property<DateTime>("NextGenerationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("next_generation_time");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("varchar(50)")
                        .HasColumnName("status");

                    b.Property<DateTime?>("LastGeneratedTimestamp")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("last_generated_timestamp");

                    b.Property<string>("LastError")
                        .HasColumnType("text")
                        .HasColumnName("last_error");

                    b.Property<int>("DefaultPoints")
                        .HasColumnType("int")
                        .HasColumnName("default_points");

                    b.Property<DateTime>("CreationTimestamp")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("creation_timestamp");

                    b.Property<DateTime>("LastUpdatedTimestamp")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("last_updated_timestamp");

                    b.HasKey("PeriodicTaskScheduleId");

                    b.HasIndex("CreatorUserId");

                    b.HasIndex("TemplateTaskId");

                    b.ToTable("periodictaskschedules", (string)null);
                });

#pragma warning restore 612, 618
        }
    }
}