// IT资产管理系统 - 应用启动配置
// 文件路径: /Startup.cs
// 功能: 配置应用服务和HTTP请求管道

using System;
using System.IO;
using System.Reflection;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.OpenApi.Models;
using Microsoft.EntityFrameworkCore;
using ItAssetsSystem.Infrastructure.Data;
using ItAssetsSystem.Core.Plugins;
using ItAssetsSystem.Core.Events;
using ItAssetsSystem.Core.Resilience;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Core.Abstractions;
using ItAssetsSystem.Core.Backup;
using ItAssetsSystem.Core.Export;
using ItAssetsSystem.Core.Import;
using ItAssetsSystem.Core.Configuration;
using ItAssetsSystem.Core.Initialization;
using ItAssetsSystem.Core.UI;
using ItAssetsSystem.Core.Location;
using ItAssetsSystem.Core.NaturalLanguage;
using ItAssetsSystem.Services;
using Serilog.Events;
using Microsoft.AspNetCore.Http;
using Serilog;
using System.Linq;
using System.Collections.Generic;
using ItAssetsSystem.Infrastructure.Data.Extensions;
using MySqlConnector;
using ItAssetsSystem.Core.Extensions;
using System.Text.Json.Serialization;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using ItAssetsSystem.Core.Middleware;
using Microsoft.EntityFrameworkCore.Diagnostics;
using ItAssetsSystem.Application.Features.Notes.Services;
using ItAssetsSystem.Application.Features.Tasks.Services;
using ItAssetsSystem.Infrastructure.Data.Repositories;
using ItAssetsSystem.Infrastructure.Services; // For PeriodicTaskGenerationService
using ItAssetsSystem.Core.Services;
using ItAssetsSystem.Application.Features.SpareParts.Services;
using Microsoft.AspNetCore.SignalR;
using System.Threading.Tasks;
using System.Web;
using ItAssetsSystem.Core.Hubs;

namespace ItAssetsSystem
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        // 配置应用服务
        public void ConfigureServices(IServiceCollection services)
        {
            // 添加HttpClient服务
            services.AddHttpClient();
            
            // 配置控制器
            services.AddControllers()
                .AddJsonOptions(options =>
                {
                    // 配置JSON序列化选项，忽略循环引用
                    options.JsonSerializerOptions.ReferenceHandler = System.Text.Json.Serialization.ReferenceHandler.IgnoreCycles;
                    options.JsonSerializerOptions.WriteIndented = true;
                    options.JsonSerializerOptions.DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull;
                    // 添加枚举字符串转换器
                    options.JsonSerializerOptions.Converters.Add(new System.Text.Json.Serialization.JsonStringEnumConverter()); 
                });
            
            // 注册SQL查询日志拦截器
            services.AddSingleton<SqlQueryLoggingInterceptor>();
            
            // 添加数据库上下文
            services.AddDbContext<AppDbContext>((provider, options) =>
            {
                var hostingEnvironment = provider.GetRequiredService<IWebHostEnvironment>();
                var isDevelopment = hostingEnvironment.IsDevelopment();
                
                try
                {
                    // 尝试连接MySQL数据库
                    var connectionString = Configuration.GetConnectionString("DefaultConnection");
                    
                    Console.WriteLine("尝试连接到MySQL...");
                    
                    // 获取SQL日志拦截器
                    var sqlInterceptor = provider.GetRequiredService<SqlQueryLoggingInterceptor>();
                    
                    // 测试连接
                    using (var conn = new MySqlConnection(connectionString))
                    {
                        try
                        {
                            conn.Open();
                            Console.WriteLine("成功连接到MySQL数据库");
                            
                            // 如果连接成功，使用MySQL并添加SQL拦截器
                            options.UseMySql(
                                connectionString,
                                ServerVersion.AutoDetect(connectionString)
                            );
                            
                            // 添加拦截器
                            options.AddInterceptors(sqlInterceptor);
                            
                            // 确保启用敏感数据日志记录
                            if (isDevelopment)
                            {
                                options.EnableSensitiveDataLogging()
                                       .EnableDetailedErrors()
                                       .LogTo(Console.WriteLine, LogLevel.Information);
                            }
                        }
                        catch
                        {
                            throw; // 重新抛出异常，让下面的catch块处理
                        }
                        finally
                        {
                            conn.Close();
                        }
                    }
                }
                catch (Exception ex)
                {
                    // 如果无法连接MySQL，尝试创建本地SQLite数据库
                    Console.WriteLine($"无法连接到MySQL数据库: {ex.Message}");
                    Console.WriteLine("请确保MySQL服务正在运行，数据库已创建，并且连接字符串正确。");
                    Console.WriteLine("请参考README文件或项目文档设置数据库。");
                    
                    // 防止应用程序崩溃
                    throw new ApplicationException("数据库连接失败。请确保MySQL服务已启动且连接字符串正确。", ex);
                }
            });
            
            // 添加事件总线和插件管理器
            services.AddEventBus();
            services.AddPluginManager();
            
            // 注册日志工厂
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.AddDebug();
            });
            
            // 注册内置插件
            services.AddSingleton<IPlugin, TaskManagementPlugin>();
            services.AddSingleton<IPlugin, AuditLogPlugin>();
            services.AddSingleton<IPlugin, FaultManagementPlugin>();
            services.AddSingleton<IPlugin, PurchaseManagementPlugin>();
            
            // 注册网络监控和离线队列服务
            ConfigureResilienceServices(services);
            
            // 注册数据备份服务
            ConfigureBackupServices(services);
            
            // 注册数据导入导出服务
            ConfigureImportExportServices(services);
            
            // 注册系统配置服务
            ConfigureSystemConfigServices(services);
            
            // 注册UI定义服务
            services.AddScoped<IUiDefinitionService, UiDefinitionService>();
            
            // 注册位置层级服务
            services.AddScoped<ILocationHierarchyService, LocationHierarchyService>();
            
            // 注册位置部门继承服务 - 已移除，因为相关文件不存在
            // services.AddScoped<Application.Features.Location.Services.ILocationDepartmentInheritanceService, Application.Features.Location.Services.LocationDepartmentInheritanceService>();
            
            // 注册自然语言处理服务
            services.AddScoped<INaturalLanguageService, NaturalLanguageService>();
            
            // 注册TokenService
            services.AddScoped<ITokenService, TokenService>();
            
            // 注册数据导入相关服务
            services.AddScoped<ItAssetsSystem.Core.Import.IImportService, ItAssetsSystem.Core.Import.ImportService>();
            services.AddScoped<ItAssetsSystem.Core.Export.IExportService, ItAssetsSystem.Core.Export.ExportService>();
            
            // 添加Swagger
            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo { 
                    Title = "IT资产管理系统API", 
                    Version = "v1",
                    Description = "IT资产管理系统API文档",
                    Contact = new OpenApiContact
                    {
                        Name = "管理员",
                        Email = "<EMAIL>"
                    }
                });
                
                // 启用XML注释
                var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
                var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
                if (File.Exists(xmlPath))
                {
                    c.IncludeXmlComments(xmlPath);
                }
            });

            // 添加内存缓存服务(性能优化)
            services.AddMemoryCache();

            // 添加核心服务
            services.AddCoreServices();

            // 添加MediatR服务
            services.AddMediatorServices();

            // 添加任务缓存服务(性能优化)
            services.AddScoped<ItAssetsSystem.Core.Services.ITaskCacheService, ItAssetsSystem.Core.Services.TaskCacheService>();

            // 添加任务管理服务
            services.AddScoped<ItAssetsSystem.Core.Abstractions.ITaskRepository, ItAssetsSystem.Infrastructure.Data.Repositories.TaskRepository>();
            services.AddScoped<ItAssetsSystem.Core.Abstractions.ICoreDataQueryService, ItAssetsSystem.Infrastructure.Services.CoreDataQueryService>();
            services.AddScoped<ItAssetsSystem.Application.Features.Tasks.Services.TaskService>();

            // Remove/Comment out old QuickNote registrations if they exist
            // services.AddScoped<ItAssetsSystem.Core.Abstractions.IQuickNoteRepository, ItAssetsSystem.Infrastructure.Data.Repositories.QuickNoteRepository>();
            // services.AddScoped<ItAssetsSystem.Application.Features.Notes.Services.IQuickNoteService, ItAssetsSystem.Application.Features.Notes.Services.QuickNoteService>();

            // Add new QuickMemo registrations
            services.AddScoped<ItAssetsSystem.Core.Abstractions.IQuickMemoRepository, ItAssetsSystem.Infrastructure.Data.Repositories.QuickMemoRepository>();
            services.AddScoped<ItAssetsSystem.Core.Abstractions.IQuickMemoCategoryRepository, ItAssetsSystem.Infrastructure.Data.Repositories.QuickMemoCategoryRepository>();
            services.AddScoped<ItAssetsSystem.Application.Features.Notes.Services.IQuickMemoService, ItAssetsSystem.Application.Features.Notes.Services.QuickMemoService>();

            // 添加JWT认证
            var secretKey = Configuration["Jwt:SecretKey"] ?? "ItAssetsSystem_SecretKey_2025_ABCDEFGHIJKLMN";
            var key = Encoding.ASCII.GetBytes(secretKey);
            
            services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(options =>
            {
                options.RequireHttpsMetadata = false; // 开发环境可设为false
                options.SaveToken = true;
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = false,
                    ValidateAudience = false,
                    ValidateLifetime = true,
                    ClockSkew = TimeSpan.Zero
                };
            });

            // Register Application Services
            services.AddScoped<IQuickMemoService, QuickMemoService>();
            services.AddScoped<TaskService>();
            // 注册优化后的资产统计服务 - 使用数据库视图提升性能
            services.AddScoped<ItAssetsSystem.Application.Features.AssetStatistics.Services.IAssetStatisticsService, ItAssetsSystem.Application.Features.AssetStatistics.Services.ViewBasedAssetStatisticsService>();
            
            // Register Repositories
            services.AddScoped<IQuickMemoRepository, QuickMemoRepository>();
            services.AddScoped<IQuickMemoCategoryRepository, QuickMemoCategoryRepository>();
            services.AddScoped<ITaskRepository, TaskRepository>();
            services.AddScoped<IUserRepository, UserRepository>();
            services.AddScoped<ItAssetsSystem.Core.Abstractions.IStatisticsRepository, ItAssetsSystem.Infrastructure.Data.Repositories.StatisticsRepository>();
            // Add other repository registrations here...

            // Register Infrastructure Services
            services.AddScoped<IFileStorageService, FileStorageService>();
            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
            services.AddScoped<ICurrentUserService, Core.Services.CurrentUserService>();
            services.AddHttpClient();

            // Register FileStorageSettings
            services.Configure<FileStorageSettings>(Configuration.GetSection(FileStorageSettings.SectionName));

            // Explicitly register IUserRepository if it's not already covered by other DI setups (e.g. AddCoreServices)
            // If an error occurs related to IUserRepository not being resolved, uncomment and verify the line below:
            // services.AddScoped<IUserRepository, UserRepository>();

            // Register IHttpContextAccessor for API controllers
            services.AddHttpContextAccessor();

            // Register PeriodicTaskGenerationService - 重新启用
            services.AddHostedService<PeriodicTaskGenerationService>();

            services.AddScoped<ItAssetsSystem.Application.Features.Tasks.Services.ITaskService, ItAssetsSystem.Application.Features.Tasks.Services.TaskService>();

            // 备品备件管理模块注册
            services.AddScoped<ISparePartRepository, SparePartRepository>();
            services.AddScoped<ISparePartTypeRepository, SparePartTypeRepository>();
            services.AddScoped<ISparePartLocationRepository, SparePartLocationRepository>();
            services.AddScoped<ISparePartTransactionRepository, SparePartTransactionRepository>();
            services.AddScoped<ISparePartService, SparePartService>();
            services.AddScoped<ISparePartTypeService, SparePartTypeService>();
            services.AddScoped<ISparePartLocationService, SparePartLocationService>();
            services.AddScoped<ISparePartTransactionService, SparePartTransactionService>();

            // 采购管理模块注册
            services.AddScoped<ItAssetsSystem.Application.Features.Purchase.Services.IPurchaseService, ItAssetsSystem.Application.Features.Purchase.Services.PurchaseService>();

            // 返厂管理模块注册
            services.AddScoped<ItAssetsSystem.Application.Features.ReturnToFactory.Services.ReturnToFactoryService>();

            // 故障管理模块注册
            services.AddScoped<ItAssetsSystem.Application.Features.Faults.Services.FaultService>();

            // 通知服务注册
            services.AddScoped<INotificationService, NotificationService>();
            
            // 事件处理器注册
            services.AddScoped<ItAssetsSystem.Core.Events.Handlers.TaskEventHandler>();

            // 注册V2统计分析服务
            services.AddMediatR(cfg => {
                cfg.RegisterServicesFromAssembly(typeof(ItAssetsSystem.Application.Features.Statistics.Queries.DynamicStatisticsQuery).Assembly);
            });

            // 注册资产快照服务
            services.AddHostedService<ItAssetsSystem.Application.Features.Statistics.Services.AssetSnapshotService>();
            services.AddScoped<ItAssetsSystem.Application.Features.Statistics.Services.AssetSnapshotService>();

            // 注册SignalR服务
            services.AddCors(options =>
            {
                options.AddPolicy("MyAllowSpecificOrigins", builder =>
                {
                    builder
                        .AllowAnyOrigin() // 允许任何来源
                        .AllowAnyMethod()
                        .AllowAnyHeader();
                });
            });
            services.AddSignalR(options => {
                // 配置SignalR选项
                options.EnableDetailedErrors = true; // 在开发环境启用详细错误信息
                options.KeepAliveInterval = TimeSpan.FromSeconds(15); // 保持连接活跃的间隔
                options.ClientTimeoutInterval = TimeSpan.FromSeconds(30); // 客户端超时时间
            }).AddJsonProtocol(options => {
                // 配置SignalR的JSON序列化选项
                options.PayloadSerializerOptions.PropertyNamingPolicy = null; // 保持属性名称原样
            });
        }

        // 配置HTTP请求管道
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            // 初始化系统目录
            InitializeDirectories(app);
            
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
                app.UseSwagger();
                app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "IT Assets System API v1"));
            }

            // 使用增强的请求日志中间件
            app.UseMiddleware<EnhancedRequestLoggingMiddleware>();
            
            // 尝试确保在日志目录下创建Debug子目录
            try
            {
                var debugLogDir = Path.Combine("Logs", "Debug");
                Directory.CreateDirectory(debugLogDir);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"创建Debug日志目录时出错: {ex.Message}");
            }
            
            // 添加请求日志和SQL日志文件
            var logDebugFilePath = Path.Combine("Logs", "Debug", "debug-.log");
            
            Log.Logger = new LoggerConfiguration()
                .MinimumLevel.Debug()
                .WriteTo.Console(restrictedToMinimumLevel: LogEventLevel.Information)
                .WriteTo.File(logDebugFilePath, 
                    rollingInterval: RollingInterval.Day,
                    outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff} [{Level:u3}] {Message:lj}{NewLine}{Exception}")
                .CreateLogger();

            // 添加简单的原始请求日志中间件
            app.Use(async (context, next) =>
            {
                // 记录请求开始
                var requestTime = DateTime.UtcNow;
                var request = context.Request;
                var requestMethod = request.Method;
                var originalUrl = $"{request.Path}{request.QueryString}";
                var absoluteUrl = $"{request.Scheme}://{request.Host}{request.Path}{request.QueryString}";
                var remoteIp = context.Connection.RemoteIpAddress?.ToString() ?? "unknown";
                var userAgent = request.Headers.TryGetValue("User-Agent", out var uaValues) ? uaValues.ToString() : "(无)";
                var referer = request.Headers.TryGetValue("Referer", out var refValues) ? refValues.ToString() : "(无)";
                
                // 记录完整的请求信息到单独的文件
                var logFileDir = Path.Combine("Logs", "Requests");
                Directory.CreateDirectory(logFileDir);
                
                // 使用当前系统日期生成日志文件名
                var logFilePath = Path.Combine(logFileDir, $"requests-{DateTime.Now:yyyy-MM-dd}.log");
                
                try
                {
                    // 使用文件共享模式打开文件，即使被其他进程占用也能写入
                    using (var fileStream = new FileStream(logFilePath, FileMode.Append, FileAccess.Write, FileShare.ReadWrite))
                    using (var writer = new StreamWriter(fileStream))
                    {
                        writer.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] REQUEST: {requestMethod} {absoluteUrl}");
                        writer.WriteLine($"IP: {remoteIp}");
                        writer.WriteLine($"Referer: {referer}");
                        writer.WriteLine($"User-Agent: {userAgent}");
                        
                        // 尝试获取并记录请求体内容
                        string requestBody = string.Empty;
                        if (request.ContentLength > 0 && request.ContentType != null && 
                            (request.ContentType.Contains("application/json") || 
                             request.ContentType.Contains("application/xml") ||
                             request.ContentType.Contains("text/plain") ||
                             request.ContentType.Contains("application/x-www-form-urlencoded")))
                        {
                            // 启用请求体重复读取
                            request.EnableBuffering();
                                
                            // 读取请求体
                            using (var reader = new StreamReader(
                                request.Body,
                                encoding: Encoding.UTF8,
                                detectEncodingFromByteOrderMarks: false,
                                leaveOpen: true))
                            {
                                requestBody = await reader.ReadToEndAsync();
                                    
                                // 重置请求体位置，以便后续中间件可以读取
                                request.Body.Position = 0;
                            }
                            
                            writer.WriteLine($"Request Body: {requestBody}");
                        }
                        
                        writer.WriteLine(new string('-', 80));
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"记录请求信息时出错: {ex.Message}");
                }
                
                // 捕获响应内容
                var originalBodyStream = context.Response.Body;
                using (var responseBodyStream = new MemoryStream())
                {
                    context.Response.Body = responseBodyStream;
                    
                    await next();
                    
                    responseBodyStream.Seek(0, SeekOrigin.Begin);
                    var responseBody = await new StreamReader(responseBodyStream).ReadToEndAsync();
                    responseBodyStream.Seek(0, SeekOrigin.Begin);
                    
                    // 将响应体复制回原始流
                    await responseBodyStream.CopyToAsync(originalBodyStream);
                    
                    // 记录响应信息
                    try
                    {
                        using (var fileStream = new FileStream(logFilePath, FileMode.Append, FileAccess.Write, FileShare.ReadWrite))
                        using (var writer = new StreamWriter(fileStream))
                        {
                            var statusCode = context.Response.StatusCode;
                            var contentType = context.Response.ContentType;
                            var elapsed = (DateTime.UtcNow - requestTime).TotalMilliseconds;
                            
                            writer.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] RESPONSE: {statusCode} ({elapsed}ms)");
                            writer.WriteLine($"Content-Type: {contentType ?? "(无)"}");
                            
                            // 如果响应体很长，截断显示
                            if (responseBody.Length > 1000)
                            {
                                writer.WriteLine($"Response Body (截断): {responseBody.Substring(0, 1000)}...");
                            }
                            else
                            {
                                writer.WriteLine($"Response Body: {responseBody}");
                            }
                            
                            writer.WriteLine(new string('=', 80));
                            writer.WriteLine();
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"记录响应信息时出错: {ex.Message}");
                    }
                }
            });

            app.UseHttpsRedirection();
            app.UseStaticFiles();

            app.UseRouting();

            // 启用身份验证和授权中间件
            app.UseAuthentication();
            app.UseAuthorization();

            app.UseCors("MyAllowSpecificOrigins");
            
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
                
                // 默认路由到前端静态文件
                endpoints.MapFallbackToFile("index.html");

                // 添加SignalR Hub映射
                endpoints.MapHub<NotificationHub>("/hubs/notification");
            });
            
            // 初始化插件系统
            InitializePluginSystem(app);
            
            // 初始化容错系统
            InitializeResilienceSystem(app);
            
            // 初始化事件订阅
            InitializeEventSubscriptions(app);
        }
        
        /// <summary>
        /// 初始化系统目录
        /// </summary>
        private void InitializeDirectories(IApplicationBuilder app)
        {
            var loggerFactory = app.ApplicationServices.GetRequiredService<ILoggerFactory>();
            var logger = loggerFactory.CreateLogger<Startup>();
            
            logger.LogInformation("初始化系统目录");
            
            var directoryInitializer = new DirectoryInitializer(
                app.ApplicationServices.GetRequiredService<IConfiguration>(),
                loggerFactory.CreateLogger<DirectoryInitializer>()
            );
            
            directoryInitializer.Initialize();
        }
        
        /// <summary>
        /// 配置韧性服务
        /// </summary>
        /// <param name="services">服务集合</param>
        private void ConfigureResilienceServices(IServiceCollection services)
        {
            // 注册网络监控选项
            services.Configure<NetworkMonitorOptions>(Configuration.GetSection("NetworkMonitor"));
            
            // 注册离线队列选项
            services.Configure<OfflineQueueOptions>(Configuration.GetSection("OfflineQueue"));
            
            // 注册网络监控服务（单例模式）
            services.AddSingleton<INetworkMonitor, NetworkMonitor>();
            
            // 注册离线队列服务（单例模式）
            services.AddSingleton<IOfflineQueue, OfflineQueue>();
        }
        
        /// <summary>
        /// 配置备份服务
        /// </summary>
        /// <param name="services">服务集合</param>
        private void ConfigureBackupServices(IServiceCollection services)
        {
            services.Configure<BackupOptions>(Configuration.GetSection("Backup"));
            // 更改为Scoped生命周期避免生命周期不兼容问题
            services.AddScoped<IBackupService, BackupService>();
        }
        
        /// <summary>
        /// 配置数据导入导出服务
        /// </summary>
        /// <param name="services">服务集合</param>
        private void ConfigureImportExportServices(IServiceCollection services)
        {
            // 注册数据导出服务
            services.AddScoped<IExportService, ExportService>();
            
            // 注册数据导入服务（依赖导出服务）
            services.AddScoped<IImportService, ImportService>();
        }
        
        /// <summary>
        /// 配置系统配置服务
        /// </summary>
        /// <param name="services">服务集合</param>
        private void ConfigureSystemConfigServices(IServiceCollection services)
        {
            // 注册系统配置服务（单例模式）
            services.AddSingleton<ISystemConfigService, SystemConfigService>();
        }
        
        /// <summary>
        /// 初始化插件系统
        /// </summary>
        private void InitializePluginSystem(IApplicationBuilder app)
        {
            // 获取日志工厂
            var loggerFactory = app.ApplicationServices.GetRequiredService<ILoggerFactory>();
            var logger = loggerFactory.CreateLogger<Startup>();
            
            logger.LogInformation("初始化插件系统");
            
            // 获取插件管理器
            var pluginManager = app.ApplicationServices.GetRequiredService<IPluginManager>();
            
            // 初始化插件管理器
            pluginManager.Initialize();
            
            // 加载所有插件
            pluginManager.LoadAllPlugins();
            
            // 启动所有插件
            pluginManager.StartAllPlugins();
        }
        
        /// <summary>
        /// 初始化韧性系统
        /// </summary>
        private void InitializeResilienceSystem(IApplicationBuilder app)
        {
            // 获取网络监控服务
            var networkMonitor = app.ApplicationServices.GetRequiredService<INetworkMonitor>();
            
            // 获取离线队列服务
            var offlineQueue = app.ApplicationServices.GetRequiredService<IOfflineQueue>();
            
            // 启动网络监控
            networkMonitor.Start();
            
            // 启用离线队列
            offlineQueue.Enable();
            
            // 加载离线队列状态
            offlineQueue.LoadQueueStateAsync().Wait();
        }
        
        /// <summary>
        /// 初始化事件订阅
        /// </summary>
        private void InitializeEventSubscriptions(IApplicationBuilder app)
        {
            using var scope = app.ApplicationServices.CreateScope();
            var eventBus = scope.ServiceProvider.GetRequiredService<IEventBus>();
            var taskEventHandler = scope.ServiceProvider.GetRequiredService<ItAssetsSystem.Core.Events.Handlers.TaskEventHandler>();
            
            // 订阅任务创建事件
            eventBus.SubscribeAsync<ItAssetsSystem.Core.Events.Tasks.TaskCreatedEvent>(async (taskCreatedEvent) => 
            {
                using var handlerScope = app.ApplicationServices.CreateScope();
                var handler = handlerScope.ServiceProvider.GetRequiredService<ItAssetsSystem.Core.Events.Handlers.TaskEventHandler>();
                await handler.HandleTaskCreatedAsync(taskCreatedEvent);
            });
        }
    }
}

// 计划行数: 100
// 实际行数: 100 