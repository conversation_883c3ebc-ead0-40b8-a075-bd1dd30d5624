// IT资产管理系统 - 程序入口
// 文件路径: /Program.cs
// 功能: 程序入口点，配置应用主机

using System;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using ItAssetsSystem.Infrastructure.Data.Extensions;
using Serilog;
using Serilog.Events;
using Microsoft.Extensions.Configuration;
using ItAssetsSystem.Core.Initialization;

namespace ItAssetsSystem
{
    public class Program
    {
        public static void Main(string[] args)
        {
            // 从appsettings.json读取配置
            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production"}.json", optional: true, reloadOnChange: true)
                .AddEnvironmentVariables()
                .Build();
                
            // 配置Serilog
            Log.Logger = new LoggerConfiguration()
                .ReadFrom.Configuration(configuration)
                .MinimumLevel.Override("ItAssetsSystem.Core.Import.ImportService", LogEventLevel.Debug)
                .Enrich.FromLogContext()
                .CreateLogger();

            try
            {
                Log.Information("启动应用程序 {@Application}", new { Application = "ItAssetsSystem" });
                
                CreateHostBuilder(args)
                    .Build()
                    .InitializeDatabaseAsync()
                    .GetAwaiter()
                    .GetResult()
                    .FixNotificationTableAsync()
                    .GetAwaiter()
                    .GetResult()
                    .FixFaultRecordTableAsync()
                    .GetAwaiter()
                    .GetResult()
                    .FixPeriodicTaskScheduleTableAsync()
                    .GetAwaiter()
                    .GetResult()
                    .Run();
            }
            catch (System.Exception ex)
            {
                Log.Fatal(ex, "应用程序启动失败 {@Application}", new { Application = "ItAssetsSystem" });
                System.Console.WriteLine($"应用程序启动失败: {ex.Message}");
            }
            finally
            {
                // 确保所有日志被刷新
                Log.CloseAndFlush();
            }
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .UseSerilog() // 使用Serilog作为日志提供程序
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.UseStartup<Startup>();
                });
    }
} 