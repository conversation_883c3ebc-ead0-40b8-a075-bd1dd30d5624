// File: Infrastructure/Data/Extensions/HostExtensions.cs
// Description: 主机扩展方法，用于初始化数据库和修复通知表结构

using ItAssetsSystem.Core.Initialization;
using ItAssetsSystem.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace ItAssetsSystem.Infrastructure.Data.Extensions
{
    /// <summary>
    /// 主机扩展类
    /// </summary>
    public static class HostExtensions
    {
        /// <summary>
        /// 修复通知表结构
        /// </summary>
        public static async Task<IHost> FixNotificationTableAsync(this IHost host)
        {
            using (var scope = host.Services.CreateScope())
            {
                var services = scope.ServiceProvider;
                try
                {
                    var context = services.GetRequiredService<AppDbContext>();
                    var logger = services.GetRequiredService<ILogger<AppDbContext>>();

                    logger.LogInformation("开始修复通知表结构");

                    // 初始化并修复通知表
                    await NotificationInitializer.InitializeNotificationTableAsync(services);

                    logger.LogInformation("通知表结构修复完成");
                }
                catch (Exception ex)
                {
                    var logger = services.GetRequiredService<ILogger<AppDbContext>>();
                    logger.LogError(ex, "修复通知表结构失败");
                }
            }

            return host;
        }

        /// <summary>
        /// 修复故障记录表结构
        /// </summary>
        public static async Task<IHost> FixFaultRecordTableAsync(this IHost host)
        {
            using (var scope = host.Services.CreateScope())
            {
                var services = scope.ServiceProvider;
                try
                {
                    var context = services.GetRequiredService<AppDbContext>();
                    var logger = services.GetRequiredService<ILogger<AppDbContext>>();

                    logger.LogInformation("开始修复故障记录表结构");

                    // 初始化并修复故障记录表
                    await FaultRecordInitializer.InitializeFaultRecordTableAsync(services);

                    logger.LogInformation("故障记录表结构修复完成");
                }
                catch (Exception ex)
                {
                    var logger = services.GetRequiredService<ILogger<AppDbContext>>();
                    logger.LogError(ex, "修复故障记录表结构失败");
                }
            }

            return host;
        }

        /// <summary>
        /// 修复周期性任务计划表结构
        /// </summary>
        public static async Task<IHost> FixPeriodicTaskScheduleTableAsync(this IHost host)
        {
            using (var scope = host.Services.CreateScope())
            {
                var services = scope.ServiceProvider;
                try
                {
                    var context = services.GetRequiredService<AppDbContext>();
                    var logger = services.GetRequiredService<ILogger<AppDbContext>>();

                    logger.LogInformation("开始修复周期性任务计划表结构");

                    // 初始化并修复周期性任务计划表
                    await PeriodicTaskScheduleInitializer.InitializePeriodicTaskScheduleTableAsync(services);

                    logger.LogInformation("周期性任务计划表结构修复完成");
                }
                catch (Exception ex)
                {
                    var logger = services.GetRequiredService<ILogger<AppDbContext>>();
                    logger.LogError(ex, "修复周期性任务计划表结构失败");
                }
            }

            return host;
        }
    }
} 