import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { taskApi } from '@/api/task'
import { ElMessage } from 'element-plus'

export const useTaskEnhancedStore = defineStore('taskEnhanced', () => {
  // 状态
  const tasks = ref([])
  const periodicSchedules = ref([])
  const taskStats = ref({
    total: 0,
    completed: 0,
    inProgress: 0,
    overdue: 0,
    todo: 0
  })
  const loading = ref(false)
  const currentTask = ref(null)
  const notifications = ref([])

  // 过滤和搜索
  const filters = ref({
    status: '',
    priority: '',
    assigneeId: '',
    assetId: '',
    locationId: '',
    dateRange: []
  })
  const searchQuery = ref('')
  
  // 分页
  const pagination = ref({
    pageNumber: 1,
    pageSize: 20,
    total: 0
  })

  // 计算属性
  const filteredTasks = computed(() => {
    let result = [...tasks.value]
    
    // 搜索过滤
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase()
      result = result.filter(task => 
        task.name?.toLowerCase().includes(query) ||
        task.description?.toLowerCase().includes(query) ||
        task.assigneeUserName?.toLowerCase().includes(query)
      )
    }
    
    // 状态过滤
    if (filters.value.status) {
      result = result.filter(task => task.status === filters.value.status)
    }
    
    // 优先级过滤
    if (filters.value.priority) {
      result = result.filter(task => task.priority === filters.value.priority)
    }
    
    // 负责人过滤
    if (filters.value.assigneeId) {
      result = result.filter(task => task.assigneeUserId === filters.value.assigneeId)
    }
    
    // 资产过滤
    if (filters.value.assetId) {
      result = result.filter(task => task.assetId === filters.value.assetId)
    }
    
    // 位置过滤
    if (filters.value.locationId) {
      result = result.filter(task => task.locationId === filters.value.locationId)
    }
    
    // 日期范围过滤
    if (filters.value.dateRange && filters.value.dateRange.length === 2) {
      const [startDate, endDate] = filters.value.dateRange
      result = result.filter(task => {
        const planEndDate = new Date(task.planEndDate)
        return planEndDate >= startDate && planEndDate <= endDate
      })
    }
    
    return result
  })

  const tasksByStatus = computed(() => {
    const grouped = {
      'Todo': [],
      'InProgress': [],
      'Done': [],
      'Cancelled': [],
      'Overdue': []
    }
    
    filteredTasks.value.forEach(task => {
      if (task.isOverdue && task.status !== 'Done') {
        grouped.Overdue.push(task)
      } else {
        grouped[task.status]?.push(task)
      }
    })
    
    return grouped
  })

  const overdueTasksCount = computed(() => {
    return tasks.value.filter(task => task.isOverdue && task.status !== 'Done').length
  })

  const upcomingDeadlines = computed(() => {
    const now = new Date()
    const threeDaysLater = new Date(now.getTime() + 3 * 24 * 60 * 60 * 1000)
    
    return tasks.value
      .filter(task => 
        task.planEndDate && 
        task.status !== 'Done' && 
        new Date(task.planEndDate) <= threeDaysLater &&
        new Date(task.planEndDate) >= now
      )
      .sort((a, b) => new Date(a.planEndDate) - new Date(b.planEndDate))
  })

  // 基本CRUD操作
  const fetchTasks = async (params = {}) => {
    loading.value = true
    try {
      const queryParams = {
        pageNumber: pagination.value.pageNumber,
        pageSize: pagination.value.pageSize,
        searchTerm: searchQuery.value,
        ...filters.value,
        ...params
      }
      
      const response = await taskApi.getTaskList(queryParams)
      if (response.success) {
        tasks.value = response.data || []
        updateTaskStats()
        return response
      } else {
        throw new Error(response.message || '获取任务列表失败')
      }
    } catch (error) {
      console.error('获取任务列表失败:', error)
      ElMessage.error(error.message || '获取任务列表失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  const getTaskById = async (taskId) => {
    try {
      const response = await taskApi.getTaskById(taskId)
      if (response.success && response.data) {
        currentTask.value = response.data
        return response.data
      } else {
        throw new Error(response.message || '获取任务详情失败')
      }
    } catch (error) {
      if (error?.response?.status === 404) {
        if (typeof window !== 'undefined' && window.ElMessage) {
          window.ElMessage.error('任务已被删除或不存在')
        }
      } else {
        if (typeof window !== 'undefined' && window.ElMessage) {
          window.ElMessage.error('获取任务详情失败: ' + (error.response?.data?.message || error.message))
        }
      }
      throw error
    }
  }

  const createTask = async (taskData) => {
    try {
      const response = await taskApi.createTask(taskData)
      if (response.success) {
        tasks.value.unshift(response.data)
        updateTaskStats()
        addNotification({
          type: 'success',
          title: '任务创建成功',
          message: `任务 "${taskData.name}" 已创建`,
          taskId: response.data.taskId
        })
        return response.data
      } else {
        throw new Error(response.message || '创建任务失败')
      }
    } catch (error) {
      console.error('创建任务失败:', error)
      throw error
    }
  }

  const updateTask = async (taskDataOrId, restDataArg) => {
    try {
      let taskId;
      let restData;
      
      // 支持两种调用方式:
      // 1. updateTask({ taskId: 123, name: "任务1" })
      // 2. updateTask(123, { name: "任务1" })
      if (typeof taskDataOrId === 'object' && taskDataOrId !== null) {
        // 第一种调用方式
        const { taskId: extractedId, ...extractedData } = taskDataOrId;
        taskId = extractedId;
        restData = extractedData;
      } else {
        // 第二种调用方式
        taskId = taskDataOrId;
        restData = restDataArg || {};
      }
      
      // 确保任务ID存在且有效
      if (!taskId || taskId === 0 || taskId === '0') {
        throw new Error('任务ID不能为空或为0')
      }
      
      console.log('准备更新任务:', { taskId, data: restData })
      
      const response = await taskApi.updateTask(taskId, restData)
      if (response.success) {
        const index = tasks.value.findIndex(t => t.taskId === taskId)
        if (index !== -1) {
          tasks.value[index] = response.data
        }
        if (currentTask.value?.taskId === taskId) {
          currentTask.value = response.data
        }
        updateTaskStats()
        addNotification({
          type: 'info',
          title: '任务更新成功',
          message: `任务 "${response.data?.name || '未命名任务'}" 已更新`,
          taskId: taskId
        })
        return response.data
      } else {
        throw new Error(response.message || '更新任务失败')
      }
    } catch (error) {
      console.error('更新任务失败:', error)
      throw error
    }
  }

  const deleteTask = async (taskId) => {
    try {
      const response = await taskApi.deleteTask(taskId)
      if (response.success) {
        const taskIndex = tasks.value.findIndex(t => t.taskId === taskId)
        if (taskIndex !== -1) {
          const deletedTask = tasks.value[taskIndex]
          tasks.value.splice(taskIndex, 1)
          updateTaskStats()
          addNotification({
            type: 'warning',
            title: '任务删除成功',
            message: `任务 "${deletedTask.name}" 已删除`,
            taskId: taskId
          })
        }
        return true
      } else {
        throw new Error(response.message || '删除任务失败')
      }
    } catch (error) {
      if (error?.response?.status === 404) {
        if (typeof window !== 'undefined' && window.ElMessage) {
          window.ElMessage.error('任务已被删除或不存在')
        }
        // 可选：自动刷新列表
        await fetchTasks()
      } else {
        if (typeof window !== 'undefined' && window.ElMessage) {
          window.ElMessage.error('删除任务失败: ' + (error.response?.data?.message || error.message))
        }
      }
      throw error
    }
  }

  // 状态管理
  const updateTaskStatus = async (taskId, newStatus, remarks = '') => {
    try {
      // 修复参数格式：传递对象而不是单独的参数
      const statusData = {
        status: newStatus,
        remarks: remarks
      }
      const response = await taskApi.updateTaskStatus(taskId, statusData)
      if (response.success) {
        const index = tasks.value.findIndex(t => t.taskId === taskId)
        if (index !== -1) {
          tasks.value[index] = response.data
        }
        updateTaskStats()
        addNotification({
          type: 'info',
          title: '任务状态更新',
          message: `任务状态已更新为 "${newStatus}"`,
          taskId: taskId
        })
        return response.data
      } else {
        throw new Error(response.message || '更新任务状态失败')
      }
    } catch (error) {
      console.error('更新任务状态失败:', error)
      throw error
    }
  }

  const updateTaskProgress = async (taskId, progress, remarks = '') => {
    try {
      // 修复参数格式：传递对象而不是单独的参数
      const progressData = {
        progress: progress,
        remarks: remarks
      }
      const response = await taskApi.updateTaskProgress(taskId, progressData)
      if (response.success) {
        const index = tasks.value.findIndex(t => t.taskId === taskId)
        if (index !== -1) {
          tasks.value[index] = response.data
        }
        updateTaskStats()
        return response.data
      } else {
        throw new Error(response.message || '更新任务进度失败')
      }
    } catch (error) {
      console.error('更新任务进度失败:', error)
      throw error
    }
  }

  const assignTask = async (taskId, assigneeUserId, remarks = '') => {
    try {
      // 修复参数格式：传递对象而不是单独的参数
      const assignData = {
        assigneeUserId: assigneeUserId,
        remarks: remarks
      }
      const response = await taskApi.assignTask(taskId, assignData)
      if (response.success) {
        const index = tasks.value.findIndex(t => t.taskId === taskId)
        if (index !== -1) {
          tasks.value[index] = response.data
        }
        addNotification({
          type: 'info',
          title: '任务分配成功',
          message: `任务已分配给 "${response.data.assigneeUserName}"`,
          taskId: taskId
        })
        return response.data
      } else {
        throw new Error(response.message || '任务分配失败')
      }
    } catch (error) {
      console.error('任务分配失败:', error)
      throw error
    }
  }

  const completeTask = async (taskId, remarks = '') => {
    try {
      // 修复参数格式：传递对象而不是单独的参数
      const completionData = {
        remarks: remarks
      }
      const response = await taskApi.completeTask(taskId, completionData)
      if (response.success) {
        const index = tasks.value.findIndex(t => t.taskId === taskId)
        if (index !== -1) {
          tasks.value[index] = response.data
        }
        updateTaskStats()
        addNotification({
          type: 'success',
          title: '任务完成',
          message: `任务 "${response.data.name}" 已完成`,
          taskId: taskId
        })
        return response.data
      } else {
        throw new Error(response.message || '完成任务失败')
      }
    } catch (error) {
      console.error('完成任务失败:', error)
      throw error
    }
  }

  // 批量操作
  const batchUpdateStatus = async (taskIds, status) => {
    try {
      const promises = taskIds.map(taskId => updateTaskStatus(taskId, status))
      await Promise.all(promises)
      addNotification({
        type: 'success',
        title: '批量状态更新成功',
        message: `${taskIds.length} 个任务状态已更新为 "${status}"`
      })
      return true
    } catch (error) {
      console.error('批量更新状态失败:', error)
      throw error
    }
  }

  const batchAssignTasks = async (taskIds, assigneeUserId) => {
    try {
      const promises = taskIds.map(taskId => assignTask(taskId, assigneeUserId))
      await Promise.all(promises)
      addNotification({
        type: 'success',
        title: '批量分配成功',
        message: `${taskIds.length} 个任务已成功分配`
      })
      return true
    } catch (error) {
      console.error('批量分配失败:', error)
      throw error
    }
  }

  const batchDeleteTasks = async (taskIds) => {
    try {
      const promises = taskIds.map(taskId => deleteTask(taskId))
      await Promise.all(promises)
      addNotification({
        type: 'warning',
        title: '批量删除成功',
        message: `${taskIds.length} 个任务已删除`
      })
      return true
    } catch (error) {
      console.error('批量删除失败:', error)
      throw error
    }
  }

  // 评论管理
  const getTaskComments = async (taskId) => {
    try {
      const response = await taskApi.getTaskComments(taskId)
      if (response.success) {
        return response.data || []
      } else {
        throw new Error(response.message || '获取评论失败')
      }
    } catch (error) {
      console.error('获取评论失败:', error)
      throw error
    }
  }

  const addComment = async (taskId, content, mentionedUserIds = []) => {
    try {
      const response = await taskApi.addComment(taskId, {
        content,
        mentionedUserIds
      })
      if (response.success) {
        // 更新当前任务的评论数
        const taskIndex = tasks.value.findIndex(t => t.taskId === taskId)
        if (taskIndex !== -1) {
          tasks.value[taskIndex].commentCount++
        }
        return response.data
      } else {
        throw new Error(response.message || '添加评论失败')
      }
    } catch (error) {
      console.error('添加评论失败:', error)
      throw error
    }
  }

  // 附件管理
  const getTaskAttachments = async (taskId) => {
    try {
      const response = await taskApi.getTaskAttachments(taskId)
      if (response.success) {
        return response.data || []
      } else {
        throw new Error(response.message || '获取附件失败')
      }
    } catch (error) {
      console.error('获取附件失败:', error)
      throw error
    }
  }

  const addAttachment = async (taskId, file, description = '') => {
    try {
      const formData = new FormData()
      formData.append('file', file)
      if (description) {
        formData.append('description', description)
      }
      
      const response = await taskApi.addAttachment(taskId, formData)
      if (response.success) {
        // 更新当前任务的附件数
        const taskIndex = tasks.value.findIndex(t => t.taskId === taskId)
        if (taskIndex !== -1) {
          tasks.value[taskIndex].attachmentCount++
        }
        return response.data
      } else {
        throw new Error(response.message || '上传附件失败')
      }
    } catch (error) {
      console.error('上传附件失败:', error)
      throw error
    }
  }

  const deleteAttachment = async (attachmentId) => {
    try {
      const response = await taskApi.deleteAttachment(attachmentId)
      if (response.success) {
        return true
      } else {
        throw new Error(response.message || '删除附件失败')
      }
    } catch (error) {
      console.error('删除附件失败:', error)
      throw error
    }
  }

  // 历史记录
  const getTaskHistory = async (taskId) => {
    try {
      const response = await taskApi.getTaskHistory(taskId)
      if (response.success) {
        return response.data || []
      } else {
        throw new Error(response.message || '获取历史记录失败')
      }
    } catch (error) {
      console.error('获取历史记录失败:', error)
      throw error
    }
  }

  // 周期性任务
  const getPeriodicSchedules = async (params = {}) => {
    try {
      const response = await taskApi.getPeriodicSchedules(params)
      if (response.success) {
        periodicSchedules.value = response.data?.items || []
        return response.data
      } else {
        throw new Error(response.message || '获取周期性任务计划失败')
      }
    } catch (error) {
      console.error('获取周期性任务计划失败:', error)
      throw error
    }
  }

  const createPeriodicSchedule = async (scheduleData) => {
    try {
      const response = await taskApi.createPeriodicSchedule(scheduleData)
      if (response.success) {
        periodicSchedules.value.unshift(response.data)
        return response.data
      } else {
        throw new Error(response.message || '创建周期性任务计划失败')
      }
    } catch (error) {
      console.error('创建周期性任务计划失败:', error)
      throw error
    }
  }

  // 辅助函数
  const updateTaskStats = () => {
    const stats = {
      total: tasks.value.length,
      completed: 0,
      inProgress: 0,
      overdue: 0,
      todo: 0
    }
    
    tasks.value.forEach(task => {
      switch (task.status) {
        case 'Done':
          stats.completed++
          break
        case 'InProgress':
          stats.inProgress++
          break
        case 'Todo':
          stats.todo++
          break
      }
      
      if (task.isOverdue && task.status !== 'Done') {
        stats.overdue++
      }
    })
    
    taskStats.value = stats
  }

  const addNotification = (notification) => {
    const newNotification = {
      id: Date.now(),
      timestamp: new Date().toISOString(),
      read: false,
      ...notification
    }
    notifications.value.unshift(newNotification)
    
    // 保持通知数量在合理范围内
    if (notifications.value.length > 100) {
      notifications.value = notifications.value.slice(0, 50)
    }
  }

  const markNotificationAsRead = (notificationId) => {
    const notification = notifications.value.find(n => n.id === notificationId)
    if (notification) {
      notification.read = true
    }
  }

  const clearAllNotifications = () => {
    notifications.value = []
  }

  // 筛选和搜索
  const setFilters = (newFilters) => {
    filters.value = { ...filters.value, ...newFilters }
  }

  const clearFilters = () => {
    filters.value = {
      status: '',
      priority: '',
      assigneeId: '',
      assetId: '',
      locationId: '',
      dateRange: []
    }
  }

  const setSearchQuery = (query) => {
    searchQuery.value = query
  }

  const setPagination = (newPagination) => {
    pagination.value = { ...pagination.value, ...newPagination }
  }

  // 重置状态
  const $reset = () => {
    tasks.value = []
    periodicSchedules.value = []
    taskStats.value = {
      total: 0,
      completed: 0,
      inProgress: 0,
      overdue: 0,
      todo: 0
    }
    loading.value = false
    currentTask.value = null
    notifications.value = []
    clearFilters()
    searchQuery.value = ''
    pagination.value = {
      pageNumber: 1,
      pageSize: 20,
      total: 0
    }
  }

  return {
    // 状态
    tasks,
    periodicSchedules,
    taskStats,
    loading,
    currentTask,
    notifications,
    filters,
    searchQuery,
    pagination,
    
    // 计算属性
    filteredTasks,
    tasksByStatus,
    overdueTasksCount,
    upcomingDeadlines,
    
    // 方法
    fetchTasks,
    getTaskById,
    createTask,
    updateTask,
    deleteTask,
    updateTaskStatus,
    updateTaskProgress,
    assignTask,
    completeTask,
    batchUpdateStatus,
    batchAssignTasks,
    batchDeleteTasks,
    getTaskComments,
    addComment,
    getTaskAttachments,
    addAttachment,
    deleteAttachment,
    getTaskHistory,
    getPeriodicSchedules,
    createPeriodicSchedule,
    addNotification,
    markNotificationAsRead,
    clearAllNotifications,
    setFilters,
    clearFilters,
    setSearchQuery,
    setPagination,
    $reset
  }
})