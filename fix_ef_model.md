# 修复EF Core模型不一致问题

## 问题
EF Core模型快照文件包含了实体类中不存在的属性，导致生成错误的SQL查询。

## 解决方案
1. 备份了现有的AppDbContextModelSnapshot.cs文件
2. 需要重新生成迁移以创建正确的模型快照

## 执行步骤
```bash
# 重新生成迁移（这会创建新的模型快照）
dotnet ef migrations add RefreshModel

# 或者直接运行应用程序，EF Core会自动更新模型
```

## 根本原因
模型快照中的PeriodicTaskSchedule实体包含了以下不存在的属性：
- DefaultAssetId
- DefaultAssigneeUserId  
- DefaultDurationDays
- DefaultLocationId
- DefaultPriority

这些属性在Domain/Entities/Tasks/PeriodicTaskSchedule.cs中并不存在，但在模型快照中存在，导致EF Core生成错误的查询。