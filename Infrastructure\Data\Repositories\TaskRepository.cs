// File: Infrastructure/Data/Repositories/TaskRepository.cs
// Description: 任务仓储实现类，实现任务相关数据访问 (V2)

#nullable enable
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ItAssetsSystem.Core.Abstractions;
using ItAssetsSystem.Infrastructure.Data; // For AppDbContext
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Linq.Expressions;

// V2 Entities from Domain layer for Task module
using ItAssetsSystem.Domain.Entities.Tasks;
// Aliases for V2 entities to avoid conflicts and for clarity
using EntityTask = ItAssetsSystem.Domain.Entities.Tasks.Task;
using EntityComment = ItAssetsSystem.Domain.Entities.Tasks.Comment;
using EntityAttachment = ItAssetsSystem.Domain.Entities.Tasks.Attachment;
using EntityTaskHistory = ItAssetsSystem.Domain.Entities.Tasks.TaskHistory;
using EntityTaskAssignee = ItAssetsSystem.Domain.Entities.Tasks.TaskAssignee;
using EntityPeriodicTaskSchedule = ItAssetsSystem.Domain.Entities.Tasks.PeriodicTaskSchedule;
using EntityPdcaPlan = ItAssetsSystem.Domain.Entities.Tasks.PdcaPlan;
// For DTO parameter type in GetPeriodicSchedulesPagedAsync
using ItAssetsSystem.Application.Features.Tasks.Dtos;

namespace ItAssetsSystem.Infrastructure.Data.Repositories
{
    /// <summary>
    /// 任务仓储实现 (V2)
    /// </summary>
    public class TaskRepository : ITaskRepository
    {
        private readonly AppDbContext _dbContext;
        private readonly ILogger<TaskRepository> _logger;

        // Define status constants at the class level for clarity and reuse
        private const int PDCA_PLAN_STATUS_IN_PROGRESS = 0; // Example value
        private const int PDCA_PLAN_STATUS_COMPLETED = 1;   // Example value
        private const int PDCA_PLAN_STATUS_CLOSED = 2;
        private const int PDCA_PLAN_STATUS_ARCHIVED = 3;

        public TaskRepository(AppDbContext dbContext, ILogger<TaskRepository> logger)
        {
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        #region 任务操作 (V2)

        public async Task<List<EntityTask>> GetAllTasksAsync()
        {
            try
            {
                return await _dbContext.Tasks.AsNoTracking().ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有任务时发生错误 (V2)");
                throw;
            }
        }

        public async Task<(List<EntityTask> Items, int TotalCount)> GetTasksPagedAsync(
            int pageNumber = 1,
            int pageSize = 10,
            string? searchTerm = null,
            string? status = null,
            string? priority = null,
            string? taskType = null,
            int? assigneeUserId = null,
            int? creatorUserId = null,
            int? assetId = null,
            int? locationId = null,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            bool includeDeleted = false,
            long? parentTaskId = null,
            long? projectId = null,
            string? sortBy = null,
            string? sortDirection = null)
        {
            try
            {
                var query = _dbContext.Tasks.AsQueryable();

                // 性能优化：预先应用最具选择性的过滤条件
                if (!includeDeleted)
                {
                    query = query.Where(t => !t.IsDeleted);
                    var inactiveStatuses = new List<string> { "Archived", "Cancelled", "Closed" };
                    query = query.Where(t => !inactiveStatuses.Contains(t.Status));
                }

                // 优化：优先应用高选择性条件以减少数据量
                if (assigneeUserId.HasValue)
                {
                    // 使用LEFT JOIN查询TaskAssignees来支持多重负责人
                    query = query.Where(t => t.AssigneeUserId == assigneeUserId.Value || 
                                             _dbContext.TaskAssignees.Any(ta => ta.TaskId == t.TaskId && ta.UserId == assigneeUserId.Value));
                }
                
                if (!string.IsNullOrWhiteSpace(status))
                {
                    query = query.Where(t => t.Status == status);
                }
                
                if (creatorUserId.HasValue)
                {
                    query = query.Where(t => t.CreatorUserId == creatorUserId.Value);
                }
                
                if (assetId.HasValue)
                {
                    query = query.Where(t => t.AssetId == assetId.Value);
                }
                
                if (locationId.HasValue)
                {
                    query = query.Where(t => t.LocationId == locationId.Value);
                }
                
                if (parentTaskId.HasValue)
                {
                    query = query.Where(t => t.ParentTaskId == parentTaskId.Value);
                }
                
                if (projectId.HasValue)
                {
                    query = query.Where(t => t.ProjectId == projectId.Value);
                }
                
                if (!string.IsNullOrWhiteSpace(priority))
                {
                    query = query.Where(t => t.Priority == priority);
                }
                
                if (!string.IsNullOrWhiteSpace(taskType))
                {
                    query = query.Where(t => t.TaskType == taskType);
                }
                
                // 日期范围过滤(通常用于索引优化)
                if (fromDate.HasValue)
                {
                    query = query.Where(t => t.CreationTimestamp >= fromDate.Value);
                }
                if (toDate.HasValue)
                {
                    query = query.Where(t => t.CreationTimestamp <= toDate.Value);
                }

                // 搜索条件放在最后(选择性较低)
                if (!string.IsNullOrWhiteSpace(searchTerm))
                {
                    query = query.Where(t => t.Name.Contains(searchTerm) || 
                                             (t.Description != null && t.Description.Contains(searchTerm)));
                }

                // 性能优化：使用预定义的排序选项而非动态表达式
                if (string.IsNullOrWhiteSpace(sortBy))
                {
                    sortBy = "CreationTimestamp";
                    sortDirection = "desc";
                }
                
                var isDesc = string.Equals(sortDirection, "desc", StringComparison.OrdinalIgnoreCase);
                
                // 使用switch优化排序性能
                query = sortBy?.ToLowerInvariant() switch
                {
                    "creationtimestamp" => isDesc ? query.OrderByDescending(t => t.CreationTimestamp) : query.OrderBy(t => t.CreationTimestamp),
                    "name" => isDesc ? query.OrderByDescending(t => t.Name) : query.OrderBy(t => t.Name),
                    "status" => isDesc ? query.OrderByDescending(t => t.Status) : query.OrderBy(t => t.Status),
                    "priority" => isDesc ? query.OrderByDescending(t => t.Priority) : query.OrderBy(t => t.Priority),
                    "progress" => isDesc ? query.OrderByDescending(t => t.Progress) : query.OrderBy(t => t.Progress),
                    "planenddate" => isDesc ? query.OrderByDescending(t => t.PlanEndDate) : query.OrderBy(t => t.PlanEndDate),
                    _ => query.OrderByDescending(t => t.CreationTimestamp) // 默认排序
                };

                // 性能优化：分别执行计数和数据查询
                var totalCount = await query.CountAsync();
                
                if (totalCount == 0)
                {
                    return (Items: new List<EntityTask>(), TotalCount: 0);
                }

                var tasks = await query
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .AsNoTracking()
                    .ToListAsync();

                // 性能优化：批量加载关联数据
                if (tasks.Count > 0)
                {
                    await LoadTaskAssigneesAsync(tasks);
                }

                return (Items: tasks, TotalCount: totalCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务分页列表时发生错误 (V2)");
                throw;
            }
        }

        /// <summary>
        /// 批量加载任务负责人信息
        /// </summary>
        private async System.Threading.Tasks.Task LoadTaskAssigneesAsync(List<EntityTask> tasks)
        {
            try
            {
                var taskIds = tasks.Select(t => t.TaskId).ToList();
                
                // 批量获取所有任务的协作者关系
                var allAssignees = await _dbContext.TaskAssignees
                    .Where(a => taskIds.Contains(a.TaskId))
                    .AsNoTracking()
                    .ToListAsync();
                
                // 按任务ID分组并分配到相应任务
                var assigneesByTaskId = allAssignees.GroupBy(a => a.TaskId)
                    .ToDictionary(g => g.Key, g => g.ToList());
                
                // 填充每个任务的Assignees集合
                foreach (var task in tasks)
                {
                    task.Assignees = assigneesByTaskId.TryGetValue(task.TaskId, out var taskAssignees)
                        ? taskAssignees
                        : new List<TaskAssignee>();
                }
                
                _logger.LogDebug("成功批量加载{TaskCount}个任务的负责人/协作者关系，共{AssigneeCount}条记录", 
                    tasks.Count, allAssignees.Count);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "批量加载任务负责人信息失败");
                // 如果批量加载失败，确保所有任务都有空的Assignees集合
                foreach (var task in tasks)
                {
                    task.Assignees ??= new List<TaskAssignee>();
                }
            }
        }

        public async Task<EntityTask?> GetTaskByIdAsync(long taskId)
        {
            try
            {
                return await _dbContext.Tasks.AsNoTracking().FirstOrDefaultAsync(t => t.TaskId == taskId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务 (ID={TaskId}) 时发生错误 (V2)", taskId);
                throw;
            }
        }

        public async Task<EntityTask?> GetTaskByIdWithDetailsAsync(long taskId)
        {
            try
            {
                // 使用分步查询代替Include，以避免导航属性映射问题
                var task = await _dbContext.Tasks
                    .AsNoTracking()
                    .FirstOrDefaultAsync(t => t.TaskId == taskId);

                if (task != null)
                {
                    // 单独加载评论
                    task.Comments = await _dbContext.Comments
                        .Where(c => c.TaskId == taskId)
                        .OrderBy(c => c.CreationTimestamp)
                        .AsNoTracking()
                        .ToListAsync();

                    // 单独加载附件 - 临时跳过以避免列映射问题
                    try 
                    {
                        task.Attachments = await _dbContext.Attachments
                            .Where(a => a.TaskId == taskId)
                            .OrderByDescending(a => a.CreationTimestamp)
                            .AsNoTracking()
                            .ToListAsync();
                    }
                    catch (Exception attachmentEx)
                    {
                        _logger.LogWarning(attachmentEx, "获取任务附件时发生错误，跳过附件加载: TaskId={TaskId}", taskId);
                        task.Attachments = new List<Attachment>();
                    }

                    // 单独加载历史记录 - 临时跳过以避免列映射问题
                    try 
                    {
                        task.History = await _dbContext.TaskHistories
                            .Where(h => h.TaskId == taskId)
                            .OrderByDescending(h => h.Timestamp)
                            .AsNoTracking()
                            .ToListAsync();
                    }
                    catch (Exception historyEx)
                    {
                        _logger.LogWarning(historyEx, "获取任务历史时发生错误，跳过历史加载: TaskId={TaskId}", taskId);
                        task.History = new List<TaskHistory>();
                    }

                    // 添加：单独加载任务负责人/协作者关系
                    try 
                    {
                        task.Assignees = await _dbContext.TaskAssignees
                            .Where(a => a.TaskId == taskId)
                            .AsNoTracking()
                            .ToListAsync();
                            
                        _logger.LogInformation("成功加载任务 {TaskId} 的 {AssigneeCount} 个负责人/协作者", taskId, task.Assignees.Count);
                    }
                    catch (Exception assigneeEx)
                    {
                        _logger.LogWarning(assigneeEx, "获取任务负责人/协作者时发生错误: TaskId={TaskId}", taskId);
                        task.Assignees = new List<TaskAssignee>();
                    }

                    // 如果有父任务，单独加载
                    if (task.ParentTaskId.HasValue)
                    {
                        task.ParentTask = await _dbContext.Tasks
                            .AsNoTracking()
                            .FirstOrDefaultAsync(t => t.TaskId == task.ParentTaskId);
                    }
                }

                return task;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务详情 (ID={TaskId}) 时发生错误 (V2)", taskId);
                throw;
            }
        }

        public async Task<string?> GetTaskNameByIdAsync(long taskId)
        {
            try
            {
                return await _dbContext.Tasks
                    .Where(t => t.TaskId == taskId)
                    .Select(t => t.Name)
                    .AsNoTracking()
                    .FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务名称 (ID={TaskId}) 时发生错误 (V2)", taskId);
                throw;
            }
        }

        public async Task<EntityTask> AddTaskAsync(EntityTask task)
        {
            try
            {
                await _dbContext.Tasks.AddAsync(task);
                await _dbContext.SaveChangesAsync();
                return task;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加任务 '{TaskName}' 时发生错误 (V2)", task.Name);
                throw;
            }
        }

        public async Task<bool> UpdateTaskAsync(EntityTask task)
        {
            try
            {
                var existingTask = await _dbContext.Tasks.FindAsync(task.TaskId);
                if (existingTask == null)
                {
                    _logger.LogWarning("尝试更新不存在的任务: ID={TaskId}", task.TaskId);
                    return false;
                }
                _dbContext.Entry(existingTask).CurrentValues.SetValues(task);
                existingTask.LastUpdatedTimestamp = DateTime.Now; // Ensure LastUpdatedTimestamp is always updated
                return await _dbContext.SaveChangesAsync() > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新任务 (ID={TaskId}) 时发生错误 (V2)", task.TaskId);
                throw;
            }
        }

        public async Task<bool> DeleteTaskAsync(long taskId)
        {
            try
            {
                var task = await _dbContext.Tasks.FindAsync(taskId);
                if (task != null)
                {
                    // For soft delete, update status or a flag
                    task.Status = "Archived"; 
                task.LastUpdatedTimestamp = DateTime.Now;
                    // _dbContext.Tasks.Remove(task); // For hard delete
                    await _dbContext.SaveChangesAsync();
                    return true;
                }
                _logger.LogWarning("尝试删除不存在的任务: ID={TaskId}", taskId);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除任务 (ID={TaskId}) 时发生错误 (V2)", taskId);
                throw;
            }
        }

        public async Task<int> GetUserTaskCountAsync(int userId, string? status = null)
        {
            try
            {
                var query = _dbContext.Tasks.Where(t => t.AssigneeUserId == userId || t.CreatorUserId == userId);
                if (!string.IsNullOrWhiteSpace(status))
                {
                    query = query.Where(t => t.Status == status);
                }
                // Filter out archived/closed tasks unless explicitly requested by status
                if (status == null || (!status.Equals("Archived", StringComparison.OrdinalIgnoreCase) && !status.Equals("Closed", StringComparison.OrdinalIgnoreCase) ))
                {
                     query = query.Where(t => t.Status != "Archived" && t.Status != "Closed");
                }

                return await query.CountAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户 (ID={UserId}) 的任务数量时发生错误 (V2)", userId);
                throw;
            }
        }

        public async Task<bool> TaskExistsAsync(long taskId)
        {
            try
            {
                return await _dbContext.Tasks.AnyAsync(t => t.TaskId == taskId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查任务 (ID={TaskId}) 是否存在时发生错误 (V2)", taskId);
                throw;
            }
        }

        public async Task<bool> UpdateTaskStatusAsync(long taskId, string newStatus, int? updatedByUserId) 
        {
            var task = await _dbContext.Tasks.FindAsync(taskId);
            if (task == null) return false;
            task.Status = newStatus;
            task.LastUpdatedTimestamp = DateTime.Now;
            // Note: updatedByUserId is not directly on V2 Task. History captures this.
            return await _dbContext.SaveChangesAsync() > 0;
        }

        public async Task<bool> AssignTaskAsync(long taskId, int assigneeUserId, int? assignedByUserId)
        {
            var task = await _dbContext.Tasks.FindAsync(taskId);
            if (task == null) return false;
            task.AssigneeUserId = assigneeUserId;
            task.LastUpdatedTimestamp = DateTime.Now;
            // Note: assignedByUserId is not directly on V2 Task. History or TaskAssignees captures this.
            return await _dbContext.SaveChangesAsync() > 0;
        }
        
        public async Task<bool> UpdateTaskProgressAsync(long taskId, int progress, int? updatedByUserId)
        {
            var task = await _dbContext.Tasks.FindAsync(taskId);
            if (task == null) return false;
            task.Progress = progress;
            task.LastUpdatedTimestamp = DateTime.Now;
            return await _dbContext.SaveChangesAsync() > 0;
        }

        public async Task<bool> CompleteTaskAsync(long taskId, int? completedByUserId)
        {
            var task = await _dbContext.Tasks.FindAsync(taskId);
            if (task == null) return false;
            task.Status = "Completed"; 
            task.ActualEndDate = DateTime.Now;
            task.Progress = 100;
            task.LastUpdatedTimestamp = DateTime.Now;
            return await _dbContext.SaveChangesAsync() > 0;
        }
        
        public async Task<bool> UpdateTaskPdcaStageAsync(long taskId, string stage, int? updatedByUserId)
        {
            var task = await _dbContext.Tasks.FindAsync(taskId);
            if (task == null) return false;
            task.PDCAStage = stage;
            task.LastUpdatedTimestamp = DateTime.Now;
                return await _dbContext.SaveChangesAsync() > 0;
            }

        public async Task<List<EntityTask>> GetOverdueTasksAsync()
        {
            return await _dbContext.Tasks
                .Where(t => t.PlanEndDate.HasValue && t.PlanEndDate < DateTime.Now && 
                            !new[] { "Completed", "Done", "Archived", "Cancelled", "Closed" }.Contains(t.Status))
                .AsNoTracking()
                .ToListAsync();
        }
        
        public async Task<int> GetSubTaskCountAsync(long parentTaskId)
        {
            return await _dbContext.Tasks.CountAsync(t => t.ParentTaskId == parentTaskId);
        }

        #endregion

        #region 评论操作 (V2)

        public async Task<List<EntityComment>> GetCommentsByTaskIdAsync(long taskId)
        {
            return await _dbContext.Comments.Where(c => c.TaskId == taskId).AsNoTracking().OrderBy(c => c.CreationTimestamp).ToListAsync();
        }

        public async Task<EntityComment?> GetCommentByIdAsync(long commentId)
        {
            return await _dbContext.Comments.AsNoTracking().FirstOrDefaultAsync(c => c.CommentId == commentId);
        }

        public async Task<EntityComment> AddCommentAsync(EntityComment comment)
        {
            await _dbContext.Comments.AddAsync(comment);
            await _dbContext.SaveChangesAsync();
            return comment;
        }

        public async Task<bool> UpdateCommentAsync(EntityComment comment)
        {
            var existingComment = await _dbContext.Comments.FindAsync(comment.CommentId);
            if (existingComment == null) return false;
            _dbContext.Entry(existingComment).CurrentValues.SetValues(comment);
            existingComment.LastUpdatedTimestamp = DateTime.Now; // Ensure update timestamp
                return await _dbContext.SaveChangesAsync() > 0;
            }

        public async Task<bool> DeleteCommentAsync(long commentId)
        {
            var comment = await _dbContext.Comments.FindAsync(commentId);
            if (comment != null)
            {
                _dbContext.Comments.Remove(comment); // Hard delete
                return await _dbContext.SaveChangesAsync() > 0;
            }
            return false;
        }
        
        public async Task<bool> PinCommentAsync(long commentId, bool isPinned)
            {
                var comment = await _dbContext.Comments.FindAsync(commentId);
            if (comment == null) return false;
            comment.IsPinned = isPinned;
            comment.LastUpdatedTimestamp = DateTime.Now;
                return await _dbContext.SaveChangesAsync() > 0;
            }

        public async Task<bool> CommentExistsAsync(long commentId, long taskId)
            {
            return await _dbContext.Comments.AnyAsync(c => c.CommentId == commentId && c.TaskId == taskId);
            }
        
        public async Task<int> GetCommentCountAsync(long taskId)
        {
            return await _dbContext.Comments.CountAsync(c => c.TaskId == taskId);
        }

        #endregion

        #region 附件操作 (V2)

        public async Task<List<EntityAttachment>> GetAttachmentsByTaskIdAsync(long taskId)
        {
            // Get attachments directly linked to the task (not via a comment)
            return await _dbContext.Attachments.Where(a => a.TaskId == taskId && a.CommentId == null).AsNoTracking().OrderByDescending(a => a.CreationTimestamp).ToListAsync();
        }
        
        public async Task<List<EntityAttachment>> GetCommentAttachmentsAsync(long commentId)
        {
             return await _dbContext.Attachments.Where(a => a.CommentId == commentId).AsNoTracking().OrderByDescending(a => a.CreationTimestamp).ToListAsync();
        }

        public async Task<EntityAttachment?> GetAttachmentByIdAsync(long attachmentId)
        {
            return await _dbContext.Attachments.AsNoTracking().FirstOrDefaultAsync(a => a.AttachmentId == attachmentId);
        }

        public async Task<EntityAttachment> AddAttachmentAsync(EntityAttachment attachment)
        {
                await _dbContext.Attachments.AddAsync(attachment);
                await _dbContext.SaveChangesAsync();
                return attachment;
            }

        public async Task<bool> UpdateAttachmentAsync(EntityAttachment attachment)
        {
            // Attachments are generally immutable once created, only metadata might change if any.
            // For now, assume no direct update or handled by delete & re-upload.
            _logger.LogWarning("UpdateAttachmentAsync called for {AttachmentId}, but V2 attachments typically aren't updated this way.", attachment.AttachmentId);
            var existingAttachment = await _dbContext.Attachments.FindAsync(attachment.AttachmentId);
            if (existingAttachment == null) return false;
            // Only update specific, mutable fields if any (e.g., FileName, Description if they were on the entity)
            // existingAttachment.FileName = attachment.FileName; // Example
            // _dbContext.Entry(existingAttachment).CurrentValues.SetValues(attachment); // Avoid if only few fields change
            return await _dbContext.SaveChangesAsync() > 0;
        }

        public async Task<bool> DeleteAttachmentAsync(long attachmentId)
        {
            var attachment = await _dbContext.Attachments.FindAsync(attachmentId);
            if (attachment != null)
            {
                _dbContext.Attachments.Remove(attachment);
                return await _dbContext.SaveChangesAsync() > 0;
            }
            return false;
        }
        
        public async Task<int> GetAttachmentCountAsync(long taskId)
        {
            return await _dbContext.Attachments.CountAsync(a => a.TaskId == taskId && a.CommentId == null);
        }

        public async Task<Dictionary<long, int>> GetSubTaskCountsBatchAsync(List<long> taskIds)
        {
            if (!taskIds.Any()) return new Dictionary<long, int>();

            var counts = await _dbContext.Tasks
                .Where(t => t.ParentTaskId.HasValue && taskIds.Contains(t.ParentTaskId.Value))
                .GroupBy(t => t.ParentTaskId!.Value)
                .Select(g => new { TaskId = g.Key, Count = g.Count() })
                .AsNoTracking()
                .ToListAsync();

            var result = taskIds.ToDictionary(id => id, id => 0);
            foreach (var count in counts)
            {
                result[count.TaskId] = count.Count;
            }
            return result;
        }

        public async Task<Dictionary<long, int>> GetCommentCountsBatchAsync(List<long> taskIds)
        {
            if (!taskIds.Any()) return new Dictionary<long, int>();

            var counts = await _dbContext.Comments
                .Where(c => taskIds.Contains(c.TaskId))
                .GroupBy(c => c.TaskId)
                .Select(g => new { TaskId = g.Key, Count = g.Count() })
                .AsNoTracking()
                .ToListAsync();

            var result = taskIds.ToDictionary(id => id, id => 0);
            foreach (var count in counts)
            {
                result[count.TaskId] = count.Count;
            }
            return result;
        }

        public async Task<Dictionary<long, int>> GetAttachmentCountsBatchAsync(List<long> taskIds)
        {
            if (!taskIds.Any()) return new Dictionary<long, int>();

            var counts = await _dbContext.Attachments
                .Where(a => a.TaskId.HasValue && taskIds.Contains(a.TaskId.Value) && a.CommentId == null)
                .GroupBy(a => a.TaskId!.Value)
                .Select(g => new { TaskId = g.Key, Count = g.Count() })
                .AsNoTracking()
                .ToListAsync();

            var result = taskIds.ToDictionary(id => id, id => 0);
            foreach (var count in counts)
            {
                result[count.TaskId] = count.Count;
            }
            return result;
        }

        public async Task<List<EntityTask>> GetTasksByIdsAsync(List<long> taskIds)
        {
            if (!taskIds.Any()) return new List<EntityTask>();

            return await _dbContext.Tasks
                .Where(t => taskIds.Contains(t.TaskId))
                .AsNoTracking()
                .ToListAsync();
        }

        #endregion

        #region 任务历史记录操作 (V2)

        public async Task<List<EntityTaskHistory>> GetHistoryByTaskIdAsync(long taskId)
        {
            return await _dbContext.TaskHistories.Where(h => h.TaskId == taskId).OrderByDescending(h => h.Timestamp).AsNoTracking().ToListAsync();
        }

        public async Task<EntityTaskHistory> AddTaskHistoryAsync(EntityTaskHistory history)
        {
            await _dbContext.TaskHistories.AddAsync(history);
            await _dbContext.SaveChangesAsync();
            return history;
        }

        #endregion
        
        #region 任务负责人操作 (V2)
        
        /// <summary>
        /// 获取任务的所有负责人/参与者
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>任务负责人/参与者列表</returns>
        public async Task<List<TaskAssignee>> GetAssigneesByTaskIdAsync(long taskId)
        {
            try
            {
                // 直接获取所有TaskAssignee记录，不要依赖导航属性
                var assignees = await _dbContext.TaskAssignees
                    .Where(a => a.TaskId == taskId)
                    .ToListAsync();
                
                return assignees;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务 {TaskId} 的负责人失败", taskId);
                return new List<TaskAssignee>();
            }
        }

        /// <summary>
        /// 添加任务负责人/参与者
        /// </summary>
        /// <param name="assignee">任务负责人/参与者</param>
        /// <returns>添加后的任务负责人/参与者实体</returns>
        public async Task<TaskAssignee> AddAssigneeAsync(TaskAssignee assignee)
        {
            try
            {
                // 检查是否已存在相同的记录
                var exists = await _dbContext.TaskAssignees.FirstOrDefaultAsync(a => 
                    a.TaskId == assignee.TaskId && 
                    a.UserId == assignee.UserId && 
                    a.AssignmentType == assignee.AssignmentType);
                
                if (exists != null)
                {
                    _logger.LogWarning("尝试添加已存在的负责人记录: TaskId={TaskId}, UserId={UserId}, Type={Type}", 
                        assignee.TaskId, assignee.UserId, assignee.AssignmentType);
                    return exists; // 返回已存在的记录
                }
                
                await _dbContext.TaskAssignees.AddAsync(assignee);
                await _dbContext.SaveChangesAsync();
                
                _logger.LogInformation("已添加任务负责人/参与者: TaskId={TaskId}, UserId={UserId}, Type={Type}", 
                    assignee.TaskId, assignee.UserId, assignee.AssignmentType);
                return assignee;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加任务负责人/参与者失败: TaskId={TaskId}, UserId={UserId}", 
                    assignee.TaskId, assignee.UserId);
                throw;
            }
        }

        /// <summary>
        /// 移除任务负责人/参与者
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="userId">用户ID</param>
        /// <param name="assignmentType">分配类型 (Assignee/Participant)</param>
        /// <returns>操作是否成功</returns>
        public async Task<bool> RemoveAssigneeAsync(long taskId, int userId, string assignmentType)
        {
            try
        {
                var assignee = await _dbContext.TaskAssignees
                    .FirstOrDefaultAsync(a => 
                        a.TaskId == taskId && 
                        a.UserId == userId && 
                        a.AssignmentType == assignmentType);
            
                if (assignee == null)
            {
                    _logger.LogWarning("尝试删除不存在的负责人记录: TaskId={TaskId}, UserId={UserId}, Type={Type}", 
                        taskId, userId, assignmentType);
                    return true; // 不存在视为成功
                }
                
                _dbContext.TaskAssignees.Remove(assignee);
                await _dbContext.SaveChangesAsync();
                
                _logger.LogInformation("已移除任务负责人/参与者: TaskId={TaskId}, UserId={UserId}, Type={Type}", 
                    taskId, userId, assignmentType);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "移除任务负责人/参与者失败: TaskId={TaskId}, UserId={UserId}", 
                    taskId, userId);
                return false;
            }
        }

        /// <summary>
        /// 添加或更新任务负责人/参与者
        /// </summary>
        /// <param name="assignee">任务负责人/参与者实体</param>
        /// <returns>操作是否成功</returns>
        public async Task<bool> AddOrUpdateAssigneeAsync(TaskAssignee assignee)
        {
            try
            {
                // 检查是否已存在相同的记录
                var existing = await _dbContext.TaskAssignees.FirstOrDefaultAsync(a => 
                    a.TaskId == assignee.TaskId && 
                    a.UserId == assignee.UserId && 
                    a.AssignmentType == assignee.AssignmentType);
                
                if (existing != null)
                {
                    // 更新现有记录
                    _dbContext.Entry(existing).CurrentValues.SetValues(assignee);
                    existing.AssignmentTimestamp = DateTime.Now;
                    _logger.LogInformation("已更新任务负责人/参与者: TaskId={TaskId}, UserId={UserId}, Type={Type}", 
                        assignee.TaskId, assignee.UserId, assignee.AssignmentType);
                }
                else
                {
                    // 添加新记录
                    await _dbContext.TaskAssignees.AddAsync(assignee);
                    _logger.LogInformation("已添加任务负责人/参与者: TaskId={TaskId}, UserId={UserId}, Type={Type}", 
                        assignee.TaskId, assignee.UserId, assignee.AssignmentType);
                }
                
                await _dbContext.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加或更新任务负责人/参与者失败: TaskId={TaskId}, UserId={UserId}", 
                    assignee.TaskId, assignee.UserId);
                return false;
            }
        }

        #endregion

        #region 周期性任务计划操作 (V2 Entities - PeriodicTaskSchedule)

        public async Task<(List<PeriodicTaskSchedule> Items, int TotalCount)> GetPeriodicSchedulesPagedAsync(PeriodicTaskScheduleQueryParametersDto parameters)
        {
            try
            {
                // Extract parameters to local variables
                int pageNumber = parameters.PageNumber <= 0 ? 1 : parameters.PageNumber;
                int pageSize = parameters.PageSize <= 0 ? 10 : parameters.PageSize;
                if (pageSize > 200) pageSize = 200;

                string? searchTerm = parameters.SearchTerm;
                string? status = parameters.Status;
                long? templateTaskId = parameters.TemplateTaskId;
                int? creatorUserId = parameters.CreatorUserId;
                string? recurrenceType = parameters.RecurrenceType;
                string? sortByInput = parameters.SortBy;
                string? sortDirectionInput = parameters.SortDirection;

                var query = _dbContext.PeriodicTaskSchedules.AsQueryable();

                // Apply filtering based on parameters
                if (!string.IsNullOrWhiteSpace(searchTerm))
                {
                    query = query.Where(s => s.Name.Contains(searchTerm) || (s.Description != null && s.Description.Contains(searchTerm)));
                }
                if (!string.IsNullOrWhiteSpace(status))
                {
                    query = query.Where(s => s.Status == status);
                }
                if (templateTaskId.HasValue)
                {
                    query = query.Where(s => s.TemplateTaskId == templateTaskId.Value);
                }
                if (creatorUserId.HasValue)
                {
                    query = query.Where(s => s.CreatorUserId == creatorUserId.Value);
                }
                if (!string.IsNullOrWhiteSpace(recurrenceType))
                {
                    query = query.Where(s => s.RecurrenceType == recurrenceType);
                }

                // Default sorting if not specified
                string sortBy = string.IsNullOrWhiteSpace(sortByInput) ? "Name" : sortByInput;
                string sortDirection = string.IsNullOrWhiteSpace(sortDirectionInput) ? "asc" : sortDirectionInput;
                
                // Dynamic sorting
                try
                {
                    var parameterExp = Expression.Parameter(typeof(EntityPeriodicTaskSchedule), "s");
                    var propertyExp = Expression.Property(parameterExp, sortBy); 
                    var lambda = Expression.Lambda<Func<EntityPeriodicTaskSchedule, object>>(Expression.Convert(propertyExp, typeof(object)), parameterExp);

                    if (string.Equals(sortDirection, "desc", StringComparison.OrdinalIgnoreCase))
                    {
                        query = query.OrderByDescending(lambda);
                    }
                    else
                    {
                        query = query.OrderBy(lambda);
                    }
                }
                catch(ArgumentException argEx) 
                {
                     _logger.LogWarning(argEx, "Invalid sort property name '{SortBy}' provided for GetPeriodicSchedulesPagedAsync. Defaulting to Name asc.", sortBy);
                     query = query.OrderBy(s => s.Name); 
                }

                var totalCount = await query.CountAsync();

                var items = await query
                    .Skip((pageNumber - 1) * pageSize) // Use local variables
                    .Take(pageSize)                  // Use local variables
                    .AsNoTracking()
                    .ToListAsync();

                return (Items: items, TotalCount: totalCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取周期性任务计划分页列表时发生错误 (V2)");
                throw;
            }
        }

        public async Task<List<PeriodicTaskSchedule>> GetAllPeriodicSchedulesAsync(bool includeDisabled = false)
        {
            var query = _dbContext.PeriodicTaskSchedules.AsQueryable();
            if (!includeDisabled)
            {
                query = query.Where(s => s.Status == "Active"); // Assuming IsEnabled is represented by Status
            }
            return await query.AsNoTracking().ToListAsync();
        }

        public async Task<List<PeriodicTaskSchedule>> GetPeriodicSchedulesDueForGenerationAsync(DateTime currentTime)
            {
                return await _dbContext.PeriodicTaskSchedules
                .Where(s => s.Status == "Active" && s.NextGenerationTime <= currentTime && (s.EndDate == null || s.EndDate >= currentTime))
                .AsNoTracking()
                .ToListAsync();
        }

        public async Task<PeriodicTaskSchedule?> GetPeriodicScheduleByIdAsync(long id) // This one is fine
        {
            return await _dbContext.PeriodicTaskSchedules.AsNoTracking().FirstOrDefaultAsync(s => s.PeriodicTaskScheduleId == id);
        }
        
        // Duplicate from interface, ensure correct one is implemented or remove from interface if not needed.
        // For now, assume the one above is the primary.
        public async Task<PeriodicTaskSchedule?> GetPeriodicTaskScheduleByIdAsync(long scheduleId, bool includeTasks = false)
        {
            var query = _dbContext.PeriodicTaskSchedules.AsNoTracking();
            if (includeTasks)
            {
                // Assuming PeriodicTaskSchedule has a navigation property 'GeneratedTasks' or similar for related Tasks.
                // query = query.Include(s => s.GeneratedTasks); 
                _logger.LogWarning("GetPeriodicTaskScheduleByIdAsync with includeTasks=true is not fully implemented for task inclusion.");
            }
            return await query.FirstOrDefaultAsync(s => s.PeriodicTaskScheduleId == scheduleId);
        }

        public async Task<string?> GetPeriodicTaskScheduleNameByIdAsync(long scheduleId)
        {
            try
            {
                // 添加调试信息
                _logger.LogInformation("正在查询周期任务计划名称，scheduleId: {ScheduleId}", scheduleId);

                // 使用原始SQL查询来避免EF Core的表名映射问题
                var connection = _dbContext.Database.GetDbConnection();
                await connection.OpenAsync();

                using var command = connection.CreateCommand();
                command.CommandText = "SELECT Name FROM periodic_task_schedules WHERE periodic_task_schedule_id = @scheduleId LIMIT 1";
                var parameter = command.CreateParameter();
                parameter.ParameterName = "@scheduleId";
                parameter.Value = scheduleId;
                command.Parameters.Add(parameter);

                var result = await command.ExecuteScalarAsync() as string;

                _logger.LogInformation("查询结果: {Result}", result ?? "null");
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "查询周期任务计划名称时发生错误，scheduleId: {ScheduleId}", scheduleId);
                return null;
            }
        }


        public async Task<PeriodicTaskSchedule> AddPeriodicScheduleAsync(PeriodicTaskSchedule schedule)
        {
                await _dbContext.PeriodicTaskSchedules.AddAsync(schedule);
                await _dbContext.SaveChangesAsync();
                return schedule;
        }

        public async Task<bool> UpdatePeriodicScheduleAsync(PeriodicTaskSchedule schedule)
        {
            var existingSchedule = await _dbContext.PeriodicTaskSchedules.FindAsync(schedule.PeriodicTaskScheduleId);
            if (existingSchedule == null) return false;
            _dbContext.Entry(existingSchedule).CurrentValues.SetValues(schedule);
            existingSchedule.LastUpdatedTimestamp = DateTime.Now;
                return await _dbContext.SaveChangesAsync() > 0;
        }

        public async Task<bool> DeletePeriodicScheduleAsync(long id) // Soft delete by changing status
            {
                var schedule = await _dbContext.PeriodicTaskSchedules.FindAsync(id);
                if (schedule == null) return false;
                schedule.Status = "Archived"; // Or "Deleted"
            schedule.LastUpdatedTimestamp = DateTime.Now;
                return await _dbContext.SaveChangesAsync() > 0;
        }

        public async Task<bool> EnablePeriodicScheduleAsync(long id, bool isEnabled)
            {
                var schedule = await _dbContext.PeriodicTaskSchedules.FindAsync(id);
                if (schedule == null) return false;
            schedule.Status = isEnabled ? "Active" : "Disabled";
            schedule.LastUpdatedTimestamp = DateTime.Now;
                return await _dbContext.SaveChangesAsync() > 0;
        }

        public async Task<bool> UpdateNextGenerationTimeAsync(long id, DateTime nextGenerationTime)
            {
                var schedule = await _dbContext.PeriodicTaskSchedules.FindAsync(id);
                if (schedule == null) return false;
                schedule.NextGenerationTime = nextGenerationTime;
            schedule.LastUpdatedTimestamp = DateTime.Now;
                return await _dbContext.SaveChangesAsync() > 0;
            }

        public async Task<bool> UpdateGenerationStatisticsAsync(long id, int generatedCount, DateTime? nextGenerationTime = null)
        {
            var schedule = await _dbContext.PeriodicTaskSchedules.FindAsync(id);
            if (schedule == null) return false;

            // 更新已生成次数
            schedule.OccurrencesGenerated += generatedCount;

            // 更新最后生成时间
            schedule.LastGeneratedTimestamp = DateTime.Now;

            // 如果提供了下次生成时间，也一起更新
            if (nextGenerationTime.HasValue)
            {
                schedule.NextGenerationTime = nextGenerationTime.Value;
            }

            schedule.LastUpdatedTimestamp = DateTime.Now;
            return await _dbContext.SaveChangesAsync() > 0;
        }

        public async Task<PeriodicTaskScheduleAssignee> AddPeriodicScheduleAssigneeAsync(PeriodicTaskScheduleAssignee assignee)
        {
            _dbContext.PeriodicTaskScheduleAssignees.Add(assignee);
            await _dbContext.SaveChangesAsync();
            return assignee;
        }

        public async Task<List<PeriodicTaskScheduleAssignee>> GetPeriodicScheduleAssigneesAsync(long scheduleId)
        {
            return await _dbContext.PeriodicTaskScheduleAssignees
                .Where(a => a.PeriodicTaskScheduleId == scheduleId)
                .OrderBy(a => a.CreatedAt)
                .ToListAsync();
        }

        public async Task<bool> DeletePeriodicScheduleAssigneesAsync(long scheduleId)
        {
            var assignees = await _dbContext.PeriodicTaskScheduleAssignees
                .Where(a => a.PeriodicTaskScheduleId == scheduleId)
                .ToListAsync();

            if (assignees.Count > 0)
            {
                _dbContext.PeriodicTaskScheduleAssignees.RemoveRange(assignees);
                await _dbContext.SaveChangesAsync();
            }

            return true;
        }
        #endregion

        #region PDCA计划操作 (V2 Entities - PdcaPlan)

        public async Task<List<EntityPdcaPlan>> GetAllPdcaPlansAsync(bool includeArchived = false)
        {
            var query = _dbContext.PdcaPlans.AsQueryable();
            if(!includeArchived)
            {
                query = query.Where(p => p.Status != PDCA_PLAN_STATUS_ARCHIVED);
            }
            return await query.AsNoTracking().ToListAsync();
        }

        public async Task<EntityPdcaPlan?> GetPdcaPlanByIdAsync(long id)
        {
            return await _dbContext.PdcaPlans.AsNoTracking().FirstOrDefaultAsync(p => p.PdcaPlanId == id);
        }

        public async Task<EntityPdcaPlan?> GetPdcaPlanByIdWithTasksAsync(long id)
        {
            try
            {
                return await _dbContext.PdcaPlans
                    .Include(p => p.Task) // Include the single associated Task (PdcaPlan.Task is the main PDCA task)
                        .ThenInclude(t => t!.SubTasks!.OrderBy(st => st.CreationTimestamp)) // Then include sub-tasks of that main Task, ordered
                    .Include(p => p.Task)
                        .ThenInclude(t => t!.Comments!.OrderBy(c => c.CreationTimestamp)) // And its comments, ordered
                    .Include(p => p.Task)
                        .ThenInclude(t => t!.Attachments!.OrderByDescending(a => a.CreationTimestamp)) // And its attachments, ordered
                    .Include(p => p.Task)
                        .ThenInclude(t => t!.Assignees) // And its assignees
                    .Include(p => p.CreatorUser) // Include PDCA plan creator
                    .Include(p => p.ResponsiblePerson) // Include PDCA plan responsible person
                    .AsNoTracking()
                    .FirstOrDefaultAsync(p => p.PdcaPlanId == id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取PDCA计划 (ID={Id}) 及其关联任务和详情时发生错误 (V2)", id);
                throw;
            }
        }

        public async Task<EntityPdcaPlan> AddPdcaPlanAsync(EntityPdcaPlan plan)
        {
                await _dbContext.PdcaPlans.AddAsync(plan);
                await _dbContext.SaveChangesAsync();
                return plan;
        }

        public async Task<bool> UpdatePdcaPlanAsync(EntityPdcaPlan plan)
        {
            var existingPlan = await _dbContext.PdcaPlans.FindAsync(plan.PdcaPlanId);
            if (existingPlan == null) return false;
             _dbContext.Entry(existingPlan).CurrentValues.SetValues(plan);
            existingPlan.LastUpdatedTimestamp = DateTime.Now;
                return await _dbContext.SaveChangesAsync() > 0;
        }

        public async Task<bool> ArchivePdcaPlanAsync(long id)
        {
            try
            {
                var plan = await _dbContext.PdcaPlans.FirstOrDefaultAsync(p => p.PdcaPlanId == id);
                if (plan == null) 
                {
                    _logger.LogWarning("尝试归档PDCA计划时未找到ID为 {PdcaPlanId} 的计划 (V2)", id);
                    return false;
                }
                // Correctly use PDCA_PLAN_STATUS_ARCHIVED
                plan.Status = PDCA_PLAN_STATUS_ARCHIVED; 
                plan.LastUpdatedTimestamp = DateTime.Now;

                _dbContext.PdcaPlans.Update(plan);
                await _dbContext.SaveChangesAsync();
                _logger.LogInformation("PDCA计划 (ID={PdcaPlanId}) 已归档 (V2)", id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "归档PDCA计划 (ID={PdcaPlanId}) 时发生错误 (V2)", id);
                throw;
            }
        }

        public async Task<bool> DeletePdcaPlanAsync(long id) // Soft delete
        {
            try
            {
                var plan = await _dbContext.PdcaPlans.FirstOrDefaultAsync(p => p.PdcaPlanId == id);
                if (plan == null) 
                {
                     _logger.LogWarning("尝试删除PDCA计划时未找到ID为 {PdcaPlanId} 的计划 (V2)", id);
                    return false;
                }
                // Correctly use PDCA_PLAN_STATUS_CLOSED for soft delete
                plan.Status = PDCA_PLAN_STATUS_CLOSED; 
                plan.LastUpdatedTimestamp = DateTime.Now;

                _dbContext.PdcaPlans.Update(plan);
                await _dbContext.SaveChangesAsync();
                _logger.LogInformation("PDCA计划 (ID={PdcaPlanId}) 已标记为删除 (状态设置为 Closed) (V2)", id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除PDCA计划 (ID={PdcaPlanId}) 时发生错误 (V2)", id);
                throw;
            }
        }

        public async Task<bool> UpdatePdcaPlanStageAsync(long id, string stage, int? updatedByUserId)
        {
            try
            {
                var plan = await _dbContext.PdcaPlans.FirstOrDefaultAsync(p => p.PdcaPlanId == id);
                if (plan == null)
                {
                    _logger.LogWarning("尝试更新PDCA计划阶段时未找到ID为 {PdcaPlanId} 的计划 (V2)", id);
                return false;
            }

                plan.Stage = stage; // Ensure 'Stage' is used
                plan.LastUpdatedTimestamp = DateTime.Now;
                
                // Note: updatedByUserId is not directly on PdcaPlan entity. 
                // If auditing is needed here, it would typically be via a history table or a separate mechanism.
                // Or, if PdcaPlan had a LastUpdatedByUserId field, it would be set here.

                _dbContext.PdcaPlans.Update(plan);
                await _dbContext.SaveChangesAsync();
                _logger.LogInformation("PDCA计划 (ID={PdcaPlanId}) 阶段已更新为 {Stage} (V2)", id, stage);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新PDCA计划 (ID={PdcaPlanId}) 阶段时发生错误 (V2)", id);
                throw;
            }
        }
        #endregion
    }
} 