#nullable enable
// File: Application/Features/Tasks/Services/TaskService.cs
// Description: 任务服务，实现任务管理业务逻辑

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json; // For MentionedUserIds serialization
using System.Threading.Tasks;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Application.Features.Tasks.Dtos;
using ItAssetsSystem.Core.Abstractions;
using ItAssetsSystem.Core.Events.Tasks;
using ItAssetsSystem.Core.Services; // 添加缓存服务命名空间
using static ItAssetsSystem.Core.Abstractions.ICoreDataQueryService;
using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Http; // Added for StatusCodes
using CronExpressionDescriptor; // 添加CronExpressionDescriptor命名空间
using TaskEntity = ItAssetsSystem.Domain.Entities.Tasks.Task;
using TaskHistory = ItAssetsSystem.Domain.Entities.Tasks.TaskHistory;
using TaskAssignee = ItAssetsSystem.Domain.Entities.Tasks.TaskAssignee;
using TaskStatus = ItAssetsSystem.Models.Enums.TaskStatus;
using TaskPriority = ItAssetsSystem.Models.Enums.TaskPriority;
using TaskType = ItAssetsSystem.Models.Enums.TaskType;
using CoreTaskStatus = ItAssetsSystem.Models.Enums.TaskStatus;
using CoreTaskPriority = ItAssetsSystem.Models.Enums.TaskPriority;
using CoreTaskType = ItAssetsSystem.Models.Enums.TaskType;
using Attachment = ItAssetsSystem.Domain.Entities.Tasks.Attachment; // Add this if not already present at the top
using Comment = ItAssetsSystem.Domain.Entities.Tasks.Comment; // Added alias for V2 Comment
using CoreTaskTypeEnum = ItAssetsSystem.Models.Enums.TaskType;

namespace ItAssetsSystem.Application.Features.Tasks.Services
{
    /// <summary>
    /// 任务服务
    /// </summary>
    public class TaskService : ITaskService
    {
        private readonly ITaskRepository _taskRepository;
        private readonly ICoreDataQueryService _coreDataQueryService;
        private readonly IMediator _mediator;
        private readonly ILogger<TaskService> _logger;
        private readonly IFileStorageService _fileStorageService; 
        private readonly INotificationService _notificationService; // 添加通知服务
        private readonly ITaskCacheService _taskCacheService; // 添加缓存服务

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="taskRepository">任务仓储</param>
        /// <param name="coreDataQueryService">核心数据查询服务</param>
        /// <param name="mediator">中介者</param>
        /// <param name="logger">日志记录器</param>
        /// <param name="fileStorageService">文件存储服务</param>
        /// <param name="notificationService">通知服务</param>
        /// <param name="taskCacheService">任务缓存服务</param>
        public TaskService(
            ITaskRepository taskRepository,
            ICoreDataQueryService coreDataQueryService,
            IMediator mediator,
            ILogger<TaskService> logger,
            IFileStorageService fileStorageService,
            INotificationService notificationService,
            ITaskCacheService taskCacheService)
        {
            _taskRepository = taskRepository ?? throw new ArgumentNullException(nameof(taskRepository));
            _coreDataQueryService = coreDataQueryService ?? throw new ArgumentNullException(nameof(coreDataQueryService));
            _mediator = mediator ?? throw new ArgumentNullException(nameof(mediator));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _fileStorageService = fileStorageService ?? throw new ArgumentNullException(nameof(fileStorageService));
            _notificationService = notificationService ?? throw new ArgumentNullException(nameof(notificationService));
            _taskCacheService = taskCacheService ?? throw new ArgumentNullException(nameof(taskCacheService));
        }

        /// <summary>
        /// 创建任务
        /// </summary>
        /// <param name="request">创建任务请求</param>
        /// <param name="currentUserId">当前用户ID</param>
        /// <returns>任务DTO</returns>
        public async Task<ApiResponse<TaskDto>> CreateTaskAsync(CreateTaskRequestDto request, int currentUserId)
        {
            try
            {
                // 验证关联实体是否存在
                if (request.AssigneeUserId.HasValue && !await _coreDataQueryService.UserExistsAsync(request.AssigneeUserId.Value))
                {
                    return ApiResponse<TaskDto>.CreateFail($"指定的负责人不存在: ID={request.AssigneeUserId.Value}");
                }

                if (request.ParentTaskId.HasValue && !await _taskRepository.TaskExistsAsync(request.ParentTaskId.Value))
                    {
                        return ApiResponse<TaskDto>.CreateFail($"指定的父任务不存在: ID={request.ParentTaskId.Value}");
                }

                // if (request.PdcaPlanId.HasValue) // PdcaPlanId is not directly on Task, it's a separate entity/table related to Task
                // {
                //     var pdcaPlan = await _taskRepository.GetPdcaPlanByIdAsync(request.PdcaPlanId.Value);
                //     if (pdcaPlan == null)
                //     {
                //         return ApiResponse<TaskDto>.CreateFail($"指定的PDCA计划不存在: ID={request.PdcaPlanId}");
                //     }
                // }

                // 创建任务实体 (V2 Task)
                var task = new TaskEntity // ItAssetsSystem.Domain.Entities.Tasks.Task
                {
                    Name = request.Name, // Changed from Title
                    Description = request.Description,
                    Status = request.Status ?? "Todo", // V2 Task.Status is string
                    Priority = request.Priority ?? "Medium", // V2 Task.Priority is string?
                    TaskType = request.TaskType ?? "Normal", // V2 Task.TaskType is string
                    CreationTimestamp = DateTime.Now, // Changed from CreatedAt, use local time
                    LastUpdatedTimestamp = DateTime.Now, // Changed from UpdatedAt, use local time
                    // 设置默认日期：如果用户没有指定，则使用当前日期为开始日期，一个月后为结束日期
                    PlanStartDate = request.PlanStartDate ?? DateTime.Today,
                    PlanEndDate = request.PlanEndDate ?? DateTime.Today.AddMonths(1),
                    Progress = 0,
                    // Tags = request.Tags, // V2 Task entity does not have a direct 'Tags' string property. Tags might be managed via a separate mechanism or a join table if needed.
                    CreatorUserId = currentUserId, // INT FK
                    AssigneeUserId = request.AssigneeUserId, // INT? FK, directly from request
                    ParentTaskId = request.ParentTaskId, // BIGINT? FK
                    PDCAStage = request.PDCAStage,
                    Points = request.Points ?? 0, // Changed from RewardPoints
                    ProjectId = request.ProjectId, // From CreateTaskRequestDto
                    AssetId = request.AssetId, // From CreateTaskRequestDto
                    LocationId = request.LocationId // From CreateTaskRequestDto
                    // ParticipantUserIds are handled via TaskAssignees collection, not a direct string property on Task
                };

                // 保存任务
                var createdTask = await _taskRepository.AddTaskAsync(task);
                if (createdTask == null)
                {
                    _logger.LogError("任务仓储未能创建任务: Name={TaskName}", task.Name);
                    return ApiResponse<TaskDto>.CreateFail("任务创建失败，请查看日志。");
                }

                // 合并所有协作者列表
                var allCollaborators = new HashSet<int>();
                
                // 添加CollaboratorUserIds中的协作者
                if (request.CollaboratorUserIds != null && request.CollaboratorUserIds.Any())
                {
                    foreach(var collaboratorId in request.CollaboratorUserIds)
                    {
                        if (collaboratorId != request.AssigneeUserId) // 避免主负责人重复
                        {
                            allCollaborators.Add(collaboratorId);
                        }
                    }
                }
                
                // 添加ParticipantUserIdsList中的协作者（保持向后兼容）
                if (request.ParticipantUserIdsList != null && request.ParticipantUserIdsList.Any())
                {
                    foreach(var participantId in request.ParticipantUserIdsList)
                    {
                        if (participantId != request.AssigneeUserId) // 避免主负责人重复
                        {
                            allCollaborators.Add(participantId);
                        }
                    }
                }

                // 处理主负责人 
                if (createdTask.AssigneeUserId.HasValue)
                        {
                            await _taskRepository.AddAssigneeAsync(new TaskAssignee
                            {
                        TaskId = createdTask.TaskId,
                        UserId = createdTask.AssigneeUserId.Value,
                                AssignedByUserId = currentUserId,
                        AssignmentType = "Assignee", // 主负责人
                                AssignmentTimestamp = DateTime.Now
                            });
                    
                    _logger.LogInformation("已添加主负责人 {UserId} 到任务 {TaskId}", 
                        createdTask.AssigneeUserId.Value, createdTask.TaskId);
                }
                
                // 处理所有协作者
                foreach (var collaboratorId in allCollaborators)
                {
                    if (await _coreDataQueryService.UserExistsAsync(collaboratorId))
                {
                     await _taskRepository.AddAssigneeAsync(new TaskAssignee
                     {
                         TaskId = createdTask.TaskId,
                            UserId = collaboratorId,
                         AssignedByUserId = currentUserId,
                            AssignmentType = "Participant", // 协作者
                         AssignmentTimestamp = DateTime.Now
                     });
                        
                        _logger.LogInformation("已添加协作者 {CollaboratorId} 到任务 {TaskId}", 
                            collaboratorId, createdTask.TaskId);
                    }
                    else
                    {
                        _logger.LogWarning("创建任务 {TaskId} 时，指定的协作者ID {CollaboratorId} 不存在，已跳过。", 
                            createdTask.TaskId, collaboratorId);
                    }
                }

                // 添加任务历史记录
                var history = new TaskHistory // Domain.Entities.Tasks.TaskHistory
                {
                    TaskId = createdTask.TaskId, // Use TaskId from V2 Task
                    UserId = currentUserId,
                    Timestamp = DateTime.Now,
                    ActionType = "CreateTask",
                    Description = $"创建任务: {createdTask.Name}" // Changed from Remarks
                };
                await _taskRepository.AddTaskHistoryAsync(history);

                // 发布任务创建事件 (ensure TaskCreatedEvent constructor matches)
                await _mediator.Publish(new TaskCreatedEvent(
                    createdTask.TaskId,         // long TaskId
                    createdTask.Name,           // string TaskName
                    createdTask.CreatorUserId,  // int CreatorUserId
                    createdTask.AssigneeUserId, // int? AssigneeUserId
                    createdTask.TaskType,       // string TaskType
                    createdTask.PlanEndDate,    // DateTime? PlanEndDate
                    createdTask.Points,         // int Points
                    createdTask.CreationTimestamp // DateTime CreationTimestamp
                ));

                // 性能优化：清除任务列表缓存 (新任务创建后需要刷新列表)
                await _taskCacheService.InvalidateTaskCacheAsync();
                _logger.LogDebug("新任务 {TaskId} 创建后已清除任务列表缓存", createdTask.TaskId);

                // 获取任务详情并返回
                var createdTaskDto = await MapTaskToDtoAsync(createdTask); // Pass the entity object

                // 发送任务分配通知
                if (request.AssigneeUserId.HasValue)
                {
                    var newAssigneeIds = new List<int> { request.AssigneeUserId.Value };
                    
                    // 如果有协作者，也添加到通知列表
                    if (request.CollaboratorUserIds != null && request.CollaboratorUserIds.Any())
                    {
                        newAssigneeIds.AddRange(request.CollaboratorUserIds);
                        // 移除可能的重复
                        newAssigneeIds = newAssigneeIds.Distinct().ToList();
                    }
                    
                    await _notificationService.NotifyTaskAssigneeChangedAsync(createdTask.TaskId, new List<int>(), newAssigneeIds, currentUserId);
                }

                return ApiResponse<TaskDto>.CreateSuccess(createdTaskDto!, "任务创建成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建任务时发生错误: {TaskName}", request.Name);
                return ApiResponse<TaskDto>.CreateFail("创建任务时发生错误: " + ex.Message);
            }
        }

        /// <summary>
        /// 获取任务列表（分页）
        /// </summary>
        /// <param name="queryParameters">查询参数</param>
        public async Task<ApiResponse<List<TaskDto>>> GetTasksAsync(TaskQueryParametersDto queryParameters)
        {
            try
            {
                // 性能优化：生成缓存键并尝试从缓存获取 (除非强制刷新)
                if (!queryParameters.ForceRefresh)
                {
                    var cacheKey = _taskCacheService.GenerateCacheKey(queryParameters);
                    var cachedResult = await _taskCacheService.GetCachedTasksAsync(cacheKey);
                    
                    if (cachedResult != null)
                    {
                        _logger.LogDebug("从缓存返回任务列表: {CacheKey}", cacheKey);
                        // 转换PaginatedResult为List格式以保持API兼容性
                        return ApiResponse<List<TaskDto>>.CreateSuccess(cachedResult.Data?.Items ?? new List<TaskDto>(), cachedResult.Message ?? "获取缓存数据成功");
                    }
                }
                else
                {
                    _logger.LogDebug("强制刷新模式，跳过缓存查询");
                }

                // Validate and extract parameters
                int pageNumber = queryParameters.PageNumber <= 0 ? 1 : queryParameters.PageNumber;
                int pageSize = queryParameters.PageSize <= 0 ? 10 : queryParameters.PageSize;
                if (pageSize > 200) pageSize = 200; // Max page size

                string? searchTerm = queryParameters.SearchTerm;
                string? status = queryParameters.Status;
                string? priority = queryParameters.Priority;
                string? taskType = queryParameters.TaskType;
                int? assigneeUserId = queryParameters.AssigneeUserId;
                int? creatorUserId = queryParameters.CreatorUserId;
                int? assetId = queryParameters.AssetId;
                int? locationId = queryParameters.LocationId;
                DateTime? fromDate = queryParameters.FromDate;
                DateTime? toDate = queryParameters.ToDate;
                bool includeDeleted = queryParameters.IncludeDeleted;
                long? parentTaskId = queryParameters.ParentTaskId;
                long? projectId = queryParameters.ProjectId;
                string? sortBy = queryParameters.SortBy;
                string? sortDirection = queryParameters.SortDirection;

                var (tasks, totalCount) = await _taskRepository.GetTasksPagedAsync(
                    pageNumber,
                    pageSize,
                    searchTerm,
                    status,
                    priority,
                    taskType,
                    assigneeUserId,
                    creatorUserId,
                    assetId,
                    locationId,
                    fromDate,
                    toDate,
                    includeDeleted,
                    parentTaskId,
                    projectId,
                    sortBy,
                    sortDirection
                );

                // 性能优化：批量映射任务DTO，避免N+1查询
                List<TaskDto> taskDtos;
                try 
                {
                    taskDtos = await MapTasksToDtosOptimizedAsync(tasks);
                    _logger.LogDebug("使用优化的批量映射方法成功处理{Count}个任务", tasks.Count);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "优化的批量映射方法失败，降级到原始方法");
                    // 降级到原始方法
                    taskDtos = new List<TaskDto>();
                    foreach (var task in tasks)
                    {
                        var taskDto = await MapTaskToDtoAsync(task);
                        if (taskDto != null)
                        {
                            taskDtos.Add(taskDto);
                        }
                    }
                }
                
                // 性能优化：将结果存入缓存
                var paginatedResult = PaginatedResult<TaskDto>.Create(
                    taskDtos,
                    totalCount,
                    pageNumber,
                    pageSize
                );
                
                // 性能优化：将结果存入缓存 (除非是强制刷新模式)
                if (!queryParameters.ForceRefresh)
                {
                    var cacheKey = _taskCacheService.GenerateCacheKey(queryParameters);
                    var responseForCache = ApiResponse<PaginatedResult<TaskDto>>.CreateSuccess(paginatedResult, "获取任务列表成功");
                    
                    // 根据查询条件决定缓存时间
                    var cacheExpiration = DetermineCacheExpiration(queryParameters);
                    await _taskCacheService.SetCachedTasksAsync(cacheKey, responseForCache, cacheExpiration);
                }
                
                return ApiResponse<List<TaskDto>>.CreateSuccess(taskDtos, "获取任务列表成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务列表时发生错误: {ExceptionMessage}", ex.Message);
                return ApiResponse<List<TaskDto>>.CreateFail("获取任务列表时发生错误: " + ex.Message);
            }
        }

        /// <summary>
        /// 根据查询参数确定缓存过期时间
        /// </summary>
        private TimeSpan DetermineCacheExpiration(TaskQueryParametersDto queryParameters)
        {
            // 高实时性要求的查询使用更短的缓存时间
            if (!string.IsNullOrEmpty(queryParameters.SearchTerm) || 
                queryParameters.Status == "InProgress" ||
                queryParameters.Status == "Pending")
            {
                return TimeSpan.FromMinutes(1); // 1分钟 (降低缓存时间提升实时性)
            }
            
            // 个人任务查询缓存时间较短 (用户经常查看自己的任务)
            if (queryParameters.AssigneeUserId.HasValue || queryParameters.CreatorUserId.HasValue)
            {
                return TimeSpan.FromMinutes(2); // 2分钟
            }
            
            // 一般查询使用标准缓存时间
            if (!string.IsNullOrEmpty(queryParameters.Status) ||
                queryParameters.Priority != null ||
                queryParameters.TaskType != null)
            {
                return TimeSpan.FromMinutes(3); // 3分钟 (适度降低)
            }
            
            // 历史和已完成任务可以缓存较长时间
            if (queryParameters.Status == "Completed" || 
                queryParameters.Status == "Archived" ||
                queryParameters.FromDate.HasValue)
            {
                return TimeSpan.FromMinutes(10); // 10分钟
            }
            
            // 默认缓存时间
            return TimeSpan.FromMinutes(5); // 5分钟
        }

        /// <summary>
        /// 获取任务详情
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <param name="includeComments">是否包含评论</param>
        /// <param name="includeAttachments">是否包含附件</param>
        /// <param name="includeHistory">是否包含历史记录</param>
        /// <returns>任务详情</returns>
        public async Task<ApiResponse<TaskDto>> GetTaskByIdAsync(long id, bool includeComments = false, bool includeAttachments = false, bool includeHistory = false)
        {
            try
            {
                var taskEntity = await _taskRepository.GetTaskByIdWithDetailsAsync(id);
                if (taskEntity == null)
                {
                    return ApiResponse<TaskDto>.CreateFail($"未找到指定的任务: ID={id}");
                }

                // 新增：根据参数加载关联数据
                var taskDto = await MapTaskToDtoAsync(taskEntity);
                if (taskDto == null)
                {
                    _logger.LogWarning("MapTaskToDtoAsync returned null for an existing TaskId {TaskId}", id);
                    return ApiResponse<TaskDto>.CreateFail($"转换任务数据时发生错误: ID={id}");
                }

                // 新增：根据参数预加载关联数据
                if (includeComments)
                {
                    var commentsResult = await GetTaskCommentsAsync(id);
                    if (commentsResult.Success && commentsResult.Data != null)
                    {
                        taskDto.Comments = commentsResult.Data;
                        taskDto.CommentCount = commentsResult.Data.Count;
                    }
                }

                if (includeAttachments)
                {
                    var attachmentsResult = await GetTaskAttachmentsAsync(id);
                    if (attachmentsResult.Success && attachmentsResult.Data != null)
                    {
                        taskDto.Attachments = attachmentsResult.Data;
                        taskDto.AttachmentCount = attachmentsResult.Data.Count;
                    }
                }

                if (includeHistory)
                {
                    var historyResult = await GetTaskHistoryAsync(id);
                    if (historyResult.Success && historyResult.Data != null)
                    {
                        taskDto.History = historyResult.Data;
                    }
                }

                return ApiResponse<TaskDto>.CreateSuccess(taskDto, "获取任务详情成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务详情时发生错误: ID={TaskId}", id);
                return ApiResponse<TaskDto>.CreateFail("获取任务详情时发生错误: " + ex.Message);
            }
        }

        /// <summary>
        /// 更新任务
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="request">更新任务请求</param>
        /// <param name="currentUserId">当前用户ID</param>
        /// <returns>任务DTO</returns>
        public async Task<ApiResponse<TaskDto>> UpdateTaskAsync(long taskId, UpdateTaskRequestDto request, int currentUserId)
        {
            try
            {
                // 添加调试日志，查看接收到的协作者数据
                _logger.LogInformation("UpdateTaskAsync: 接收到协作者数据 CollaboratorUserIds: {CollaboratorUserIds}", 
                    request.CollaboratorUserIds != null ? string.Join(",", request.CollaboratorUserIds) : "null");
                
                var existingTask = await _taskRepository.GetTaskByIdAsync(taskId);
                if (existingTask == null)
                {
                    return ApiResponse<TaskDto>.CreateFail($"未找到要更新的任务: ID={taskId}");
                }

                var taskToUpdate = new TaskEntity
                {
                    TaskId = taskId,
                    Name = request.Name ?? existingTask.Name,
                    Description = request.Description ?? existingTask.Description,
                    Status = request.Status ?? existingTask.Status,
                    Priority = request.Priority ?? existingTask.Priority,
                    TaskType = request.TaskType ?? existingTask.TaskType,
                    // 更新时只有用户明确提供了日期才更新，否则保持原值
                    PlanStartDate = request.PlanStartDate ?? existingTask.PlanStartDate,
                    PlanEndDate = request.PlanEndDate ?? existingTask.PlanEndDate,
                    ActualStartDate = request.ActualStartDate ?? existingTask.ActualStartDate,
                    ActualEndDate = request.ActualEndDate ?? existingTask.ActualEndDate,
                    AssigneeUserId = request.AssigneeUserId ?? existingTask.AssigneeUserId,
                    ParentTaskId = request.ParentTaskId ?? existingTask.ParentTaskId,
                    PDCAStage = request.PDCAStage ?? existingTask.PDCAStage,
                    Points = request.Points ?? existingTask.Points,
                    Progress = request.Progress ?? existingTask.Progress,
                    AssetId = request.AssetId ?? existingTask.AssetId,
                    LocationId = request.LocationId ?? existingTask.LocationId,
                    ProjectId = request.ProjectId ?? existingTask.ProjectId,
                    IsOverdueAcknowledged = request.IsOverdueAcknowledged ?? existingTask.IsOverdueAcknowledged,
                    CreatorUserId = existingTask.CreatorUserId,
                    CreationTimestamp = existingTask.CreationTimestamp,
                    LastUpdatedTimestamp = DateTime.Now
                };

                if (request.AssigneeUserId.HasValue && existingTask.AssigneeUserId != request.AssigneeUserId && 
                    !await _coreDataQueryService.UserExistsAsync(request.AssigneeUserId.Value))
                {
                        return ApiResponse<TaskDto>.CreateFail($"指定的负责人不存在: ID={request.AssigneeUserId.Value}");
                }

                if (request.AssetId.HasValue && existingTask.AssetId != request.AssetId && 
                    !await _coreDataQueryService.AssetExistsAsync(request.AssetId.Value))
                {
                        return ApiResponse<TaskDto>.CreateFail($"指定的资产不存在: ID={request.AssetId.Value}");
                }

                if (request.LocationId.HasValue && existingTask.LocationId != request.LocationId && 
                    !await _coreDataQueryService.LocationExistsAsync(request.LocationId.Value))
                {
                        return ApiResponse<TaskDto>.CreateFail($"指定的位置不存在: ID={request.LocationId.Value}");
                }

                string changeDetails = await GenerateChangeDescriptionAsync(existingTask, taskToUpdate, _coreDataQueryService); 
                bool hasChanges = !string.IsNullOrEmpty(changeDetails);
                
                // 检查协作者变更
                if (!hasChanges && request.CollaboratorUserIds != null)
                {
                    // 获取当前协作者列表
                    var currentAssignees = await _taskRepository.GetAssigneesByTaskIdAsync(taskId);
                    var currentCollaborators = currentAssignees
                        .Where(a => a.AssignmentType == "Participant")
                        .Select(a => a.UserId)
                        .OrderBy(id => id)
                        .ToList();
                    var newCollaborators = request.CollaboratorUserIds.OrderBy(id => id).ToList();
                    
                    // 比较协作者列表是否有变更
                    if (!currentCollaborators.SequenceEqual(newCollaborators))
                    {
                        hasChanges = true;
                        changeDetails = "更新了协作者列表";
                        _logger.LogInformation("检测到协作者变更: 当前 [{Current}] -> 新 [{New}]", 
                            string.Join(",", currentCollaborators), 
                            string.Join(",", newCollaborators));
                    }
                }

                if (hasChanges)
                {
                    bool updateSuccess = await _taskRepository.UpdateTaskAsync(taskToUpdate);
                    if (!updateSuccess)
                    {
                        return ApiResponse<TaskDto>.CreateFail("数据库更新任务失败");
                    }

                    // 性能优化：清除相关缓存
                    await _taskCacheService.InvalidateTaskCacheAsync(taskId);
                    _logger.LogDebug("已清除任务 {TaskId} 相关缓存", taskId);

                    // 处理协作者更新
                    if (request.CollaboratorUserIds != null)
                    {
                        // 获取当前任务的所有分配记录
                        var currentAssignees = await _taskRepository.GetAssigneesByTaskIdAsync(taskId);
                        
                        _logger.LogInformation("更新任务 {TaskId} 的协作者，当前协作者: {CurrentAssignees}", 
                            taskId, 
                            string.Join(", ", currentAssignees.Select(a => $"{a.UserId}({a.AssignmentType})")));
                        
                        // 删除所有现有的协作者记录，保留主负责人记录
                        foreach(var existingAssignee in currentAssignees)
                        {
                            // 跳过主负责人
                            if (existingAssignee.UserId == taskToUpdate.AssigneeUserId && 
                                existingAssignee.AssignmentType == "Assignee")
                                continue;
                                
                            // 移除所有协作者
                            await _taskRepository.RemoveAssigneeAsync(taskId, existingAssignee.UserId, existingAssignee.AssignmentType);
                            
                            _logger.LogInformation("已删除任务 {TaskId} 的现有协作者 {UserId}", taskId, existingAssignee.UserId);
                        }
                        
                        // 添加新的协作者记录
                        foreach(var collaboratorId in request.CollaboratorUserIds)
                        {
                            // 跳过主负责人 (如果包含在协作者列表中)
                            if (collaboratorId == taskToUpdate.AssigneeUserId)
                                continue;
                                
                            if (await _coreDataQueryService.UserExistsAsync(collaboratorId))
                            {
                                await _taskRepository.AddAssigneeAsync(new TaskAssignee
                                {
                                    TaskId = taskId,
                                    UserId = collaboratorId,
                                    AssignedByUserId = currentUserId,
                                    AssignmentType = "Participant", // 协作者
                                    AssignmentTimestamp = DateTime.Now
                                });
                                _logger.LogInformation("已添加协作者 {UserId} 到任务 {TaskId}", collaboratorId, taskId);
                            }
                            else
                            {
                                _logger.LogWarning("更新任务 {TaskId} 时，指定的协作者ID {CollaboratorId} 不存在，已跳过。", 
                                    taskId, collaboratorId);
                            }
                        }
                        
                        // 确保主负责人记录存在
                        if (taskToUpdate.AssigneeUserId.HasValue)
                        {
                            var hasMainAssignee = (await _taskRepository.GetAssigneesByTaskIdAsync(taskId))
                                .Any(a => a.UserId == taskToUpdate.AssigneeUserId && a.AssignmentType == "Assignee");
                            
                            if (!hasMainAssignee)
                            {
                                await _taskRepository.AddAssigneeAsync(new TaskAssignee
                                {
                                    TaskId = taskId,
                                    UserId = taskToUpdate.AssigneeUserId.Value,
                                    AssignedByUserId = currentUserId,
                                    AssignmentType = "Assignee", // 主负责人
                                    AssignmentTimestamp = DateTime.Now
                                });
                                _logger.LogInformation("已添加主负责人 {UserId} 到任务 {TaskId}", taskToUpdate.AssigneeUserId.Value, taskId);
                            }
                        }
                        
                        // 添加协作者变更记录到历史
                        changeDetails += "; 更新了协作者列表";
                    }
                    else if (request.AssigneeUserId.HasValue && 
                             existingTask.AssigneeUserId != request.AssigneeUserId)
                    {
                        // 如果只更新了主负责人但没有提供协作者列表，则只更新主负责人
                        // 获取当前任务的所有分配记录
                        var currentAssignees = await _taskRepository.GetAssigneesByTaskIdAsync(taskId);
                        
                        // 查找当前主负责人记录
                        var mainAssignee = currentAssignees.FirstOrDefault(a => 
                            a.AssignmentType == "Assignee");
                        
                        if (mainAssignee != null)
                        {
                            // 删除现有主负责人记录
                            await _taskRepository.RemoveAssigneeAsync(taskId, mainAssignee.UserId, "Assignee");
                            _logger.LogInformation("已删除任务 {TaskId} 的现有主负责人 {UserId}", taskId, mainAssignee.UserId);
                        }
                        
                        // 添加新的主负责人记录
                        await _taskRepository.AddAssigneeAsync(new TaskAssignee
                        {
                            TaskId = taskId,
                            UserId = request.AssigneeUserId.Value,
                            AssignedByUserId = currentUserId,
                            AssignmentType = "Assignee", // 主负责人
                            AssignmentTimestamp = DateTime.Now
                        });
                        _logger.LogInformation("已添加新主负责人 {UserId} 到任务 {TaskId}", request.AssigneeUserId.Value, taskId);
                        
                        changeDetails += "; 更新了主负责人";
                    }

                    var history = new TaskHistory
                    {
                        TaskId = taskId,
                        UserId = currentUserId,
                        Timestamp = DateTime.Now,
                        ActionType = "UpdateTask",
                        Description = $"更新任务详情: {changeDetails.TrimEnd(';', ' ')}"
                    };
                    await _taskRepository.AddTaskHistoryAsync(history);
                    
                    // 发送任务内容变更通知
                    await _notificationService.NotifyTaskContentChangedAsync(taskId, changeDetails.TrimEnd(';', ' '), currentUserId);
                }
                else
                {
                    _logger.LogInformation("任务 {TaskId} 未检测到变更，无需更新。", taskId);
                }

                var updatedTaskEntity = await _taskRepository.GetTaskByIdWithDetailsAsync(taskId);
                var updatedTaskDto = await MapTaskToDtoAsync(updatedTaskEntity!);

                // 记录旧的负责人IDs，用于任务通知
                var oldAssigneeId = existingTask.AssigneeUserId;
                var oldAssigneeIds = new List<int>();
                if (oldAssigneeId.HasValue)
                {
                    oldAssigneeIds.Add(oldAssigneeId.Value);
                }
                
                // 获取当前任务的所有协作者
                var existingAssignees = await _taskRepository.GetAssigneesByTaskIdAsync(taskId);
                foreach (var assignee in existingAssignees)
                {
                    if (assignee.UserId != oldAssigneeId)
                    {
                        oldAssigneeIds.Add(assignee.UserId);
                    }
                }

                // 处理完所有更新后，如果负责人变更了，发送通知
                if (request.AssigneeUserId.HasValue && request.AssigneeUserId != oldAssigneeId)
                {
                    var newAssigneeIds = new List<int>();
                    if (request.AssigneeUserId.HasValue)
                    {
                        newAssigneeIds.Add(request.AssigneeUserId.Value);
                    }
                    
                    // 添加协作者IDs
                    if (request.CollaboratorUserIds != null)
                    {
                        newAssigneeIds.AddRange(request.CollaboratorUserIds);
                    }
                    
                    // 移除重复项
                    newAssigneeIds = newAssigneeIds.Distinct().ToList();
                    
                    await _notificationService.NotifyTaskAssigneeChangedAsync(taskId, oldAssigneeIds, newAssigneeIds, currentUserId);
                }
                // 如果只是协作者变更了，也发送通知
                else if (request.CollaboratorUserIds != null)
                {
                    var newAssigneeIds = new List<int>();
                    if (taskToUpdate.AssigneeUserId.HasValue)
                    {
                        newAssigneeIds.Add(taskToUpdate.AssigneeUserId.Value);
                    }
                    
                    // 添加协作者IDs
                    newAssigneeIds.AddRange(request.CollaboratorUserIds);
                    
                    // 移除重复项
                    newAssigneeIds = newAssigneeIds.Distinct().ToList();
                    
                    // 检查是否有变化
                    if (!oldAssigneeIds.OrderBy(id => id).SequenceEqual(newAssigneeIds.OrderBy(id => id)))
                    {
                        await _notificationService.NotifyTaskAssigneeChangedAsync(taskId, oldAssigneeIds, newAssigneeIds, currentUserId);
                    }
                }
                
                // 如果状态发生变化，发送状态变更通知
                if (existingTask.Status != taskToUpdate.Status)
                {
                    await _notificationService.NotifyTaskStatusChangedAsync(taskId, existingTask.Status, taskToUpdate.Status, currentUserId);
                }

                return ApiResponse<TaskDto>.CreateSuccess(updatedTaskDto!, hasChanges ? "任务更新成功" : "任务无变更");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新任务时发生错误: ID={TaskId}", taskId);
                return ApiResponse<TaskDto>.CreateFail("更新任务时发生错误: " + ex.Message);
            }
        }

        private async Task<string> GenerateChangeDescriptionAsync(TaskEntity oldTask, TaskEntity newTask, ICoreDataQueryService queryService)
        {
            var changes = new List<string>();
            if (oldTask.Name != newTask.Name) changes.Add($"名称从 '{oldTask.Name}' 变为 '{newTask.Name}'");
            if (oldTask.Description != newTask.Description) changes.Add("描述已更改"); 
            if (oldTask.Status != newTask.Status) changes.Add($"状态从 '{oldTask.Status}' 变为 '{newTask.Status}'");
            if (oldTask.Priority != newTask.Priority) changes.Add($"优先级从 '{oldTask.Priority}' 变为 '{newTask.Priority}'");
            if (oldTask.TaskType != newTask.TaskType) changes.Add($"类型从 '{oldTask.TaskType}' 变为 '{newTask.TaskType}'");
            if (oldTask.PlanStartDate != newTask.PlanStartDate) changes.Add($"计划开始从 '{oldTask.PlanStartDate:yyyy-MM-dd}' 变为 '{newTask.PlanStartDate:yyyy-MM-dd}'");
            if (oldTask.PlanEndDate != newTask.PlanEndDate) changes.Add($"计划结束从 '{oldTask.PlanEndDate:yyyy-MM-dd}' 变为 '{newTask.PlanEndDate:yyyy-MM-dd}'");
            if (oldTask.ActualStartDate != newTask.ActualStartDate) changes.Add($"实际开始从 '{oldTask.ActualStartDate:yyyy-MM-dd}' 变为 '{newTask.ActualStartDate:yyyy-MM-dd}'");
            if (oldTask.ActualEndDate != newTask.ActualEndDate) changes.Add($"实际结束从 '{oldTask.ActualEndDate:yyyy-MM-dd}' 变为 '{newTask.ActualEndDate:yyyy-MM-dd}'");
            if (oldTask.Progress != newTask.Progress) changes.Add($"进度从 {oldTask.Progress}% 变为 {newTask.Progress}%");
            if (oldTask.Points != newTask.Points) changes.Add($"积分从 {oldTask.Points} 变为 {newTask.Points}");
            if (oldTask.PDCAStage != newTask.PDCAStage) changes.Add($"PDCA阶段从 '{oldTask.PDCAStage}' 变为 '{newTask.PDCAStage}'");
            if (oldTask.IsOverdueAcknowledged != newTask.IsOverdueAcknowledged) changes.Add($"逾期确认状态从 '{oldTask.IsOverdueAcknowledged}' 变为 '{newTask.IsOverdueAcknowledged}'");

            if (oldTask.AssigneeUserId != newTask.AssigneeUserId)
            {
                var oldAssigneeName = oldTask.AssigneeUserId.HasValue ? (await queryService.GetUserAsync(oldTask.AssigneeUserId.Value))?.Username ?? "未知" : "未分配";
                var newAssigneeName = newTask.AssigneeUserId.HasValue ? (await queryService.GetUserAsync(newTask.AssigneeUserId.Value))?.Username ?? "未知" : "未分配";
                changes.Add($"负责人从 '{oldAssigneeName}' 变为 '{newAssigneeName}'");
            }
            if (oldTask.AssetId != newTask.AssetId)
            {
                var oldAssetName = oldTask.AssetId.HasValue ? (await queryService.GetAssetAsync(oldTask.AssetId.Value))?.Name ?? "未知" : "无";
                var newAssetName = newTask.AssetId.HasValue ? (await queryService.GetAssetAsync(newTask.AssetId.Value))?.Name ?? "未知" : "无";
                changes.Add($"关联资产从 '{oldAssetName}' 变为 '{newAssetName}'");
            }
             if (oldTask.LocationId != newTask.LocationId)
            {
                var oldLocationName = oldTask.LocationId.HasValue ? (await queryService.GetLocationAsync(oldTask.LocationId.Value))?.Name ?? "未知" : "无";
                var newLocationName = newTask.LocationId.HasValue ? (await queryService.GetLocationAsync(newTask.LocationId.Value))?.Name ?? "未知" : "无";
                changes.Add($"关联位置从 '{oldLocationName}' 变为 '{newLocationName}'");
            }
            if (oldTask.ProjectId != newTask.ProjectId)
            {
                changes.Add($"关联项目从 '{oldTask.ProjectId?.ToString() ?? "无"}' 变为 '{newTask.ProjectId?.ToString() ?? "无"}'");
            }
            if (oldTask.ParentTaskId != newTask.ParentTaskId)
            {
                var oldParentTaskName = oldTask.ParentTaskId.HasValue ? (await _taskRepository.GetTaskNameByIdAsync(oldTask.ParentTaskId.Value)) ?? "未知" : "无";
                var newParentTaskName = newTask.ParentTaskId.HasValue ? (await _taskRepository.GetTaskNameByIdAsync(newTask.ParentTaskId.Value)) ?? "未知" : "无";
                changes.Add($"父任务从 '{oldParentTaskName}' 变为 '{newParentTaskName}'");
            }

            return string.Join("; ", changes);
        }

        /// <summary>
        /// 删除任务
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <param name="currentUserId">当前用户ID</param>
        /// <returns>操作结果</returns>
        public async Task<ApiResponse<bool>> DeleteTaskAsync(long id, int currentUserId)
        {
            try
            {
                var task = await _taskRepository.GetTaskByIdAsync(id);
                if (task == null)
                {
                    return ApiResponse<bool>.CreateFail($"未找到要删除的任务: ID={id}");
                }

                // 标准软删除：设置 IsDeleted=true，兼容 Status=Archived
                task.IsDeleted = true;
                task.Status = "Archived";
                task.LastUpdatedTimestamp = DateTime.Now;
                await _taskRepository.UpdateTaskAsync(task);

                var history = new TaskHistory
                {
                    TaskId = id,
                    UserId = currentUserId,
                    Timestamp = DateTime.Now,
                    ActionType = "DeleteTask",
                    Description = $"任务已软删除 (归档): {task.Name}"
                };
                await _taskRepository.AddTaskHistoryAsync(history);

                await _mediator.Publish(new TaskDeletedEvent(id, task.Name, currentUserId));

                // 性能优化：清除任务列表缓存 (任务删除后需要刷新列表)
                await _taskCacheService.InvalidateTaskCacheAsync(id);
                _logger.LogDebug("任务 {TaskId} 删除后已清除任务列表缓存", id);

                return ApiResponse<bool>.CreateSuccess(true, "任务软删除成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "软删除任务时发生错误: ID={TaskId}", id);
                return ApiResponse<bool>.CreateFail("软删除任务时发生错误: " + ex.Message);
            }
        }

        /// <summary>
        /// 获取任务优先级列表
        /// </summary>
        /// <returns>任务优先级列表</returns>
        public List<string> GetTaskPriorities()
        {
            // Define the standard string values for priorities
            return new List<string> { "Low", "Medium", "High", "Urgent" };
        }

        /// <summary>
        /// 获取任务类型列表
        /// </summary>
        /// <returns>任务类型列表</returns>
        public List<string> GetTaskTypes()
        {
            // Define the standard string values for task types
            return new List<string> { "Normal", "Periodic", "PDCA" };
        }

        /// <summary>
        /// 获取任务状态列表
        /// </summary>
        /// <returns>任务状态列表</returns>
        public List<string> GetTaskStatuses()
        {
            // Define the standard string values for statuses
            return new List<string> { "Todo", "InProgress", "Review", "Done", "Cancelled", "Archived" };
        }

        /// <summary>
        /// 更新任务状态
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="newStatus">新状态</param>
        /// <param name="remarks">备注</param>
        /// <param name="currentUserId">当前用户ID</param>
        /// <returns>任务DTO</returns>
        public async Task<ApiResponse<TaskDto>> UpdateTaskStatusAsync(long taskId, string newStatus, string? remarks, int currentUserId)
        {
            try
            {
                var task = await _taskRepository.GetTaskByIdAsync(taskId);
                if (task == null)
                {
                    return ApiResponse<TaskDto>.CreateFail($"未找到任务: ID={taskId}");
                }

                var oldStatus = task.Status;
                task.Status = newStatus;
                task.LastUpdatedTimestamp = DateTime.Now;
                
                if ((newStatus.Equals("Completed", StringComparison.OrdinalIgnoreCase) || newStatus.Equals("Done", StringComparison.OrdinalIgnoreCase)) && task.ActualEndDate == null)
                {
                    task.ActualEndDate = DateTime.Now;
                    task.Progress = 100;
                }

                await _taskRepository.UpdateTaskAsync(task);

                var history = new TaskHistory
                {
                    TaskId = taskId,
                    UserId = currentUserId,
                    Timestamp = DateTime.Now,
                    ActionType = "StatusChange",
                    OldValue = oldStatus,
                    NewValue = newStatus,
                    Description = remarks 
                };
                await _taskRepository.AddTaskHistoryAsync(history);

                // 发送任务状态变更通知
                await _notificationService.NotifyTaskStatusChangedAsync(taskId, oldStatus, newStatus, currentUserId);

                var updatedTaskDto = await MapTaskToDtoAsync(task);
                return ApiResponse<TaskDto>.CreateSuccess(updatedTaskDto!, "任务状态更新成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新任务状态时发生错误: TaskId={TaskId}", taskId);
                return ApiResponse<TaskDto>.CreateFail("更新任务状态时发生错误: " + ex.Message);
            }
        }

        /// <summary>
        /// 更新任务进度
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="progress">新进度</param>
        /// <param name="remarks">备注</param>
        /// <param name="currentUserId">当前用户ID</param>
        /// <returns>任务DTO</returns>
        public async Task<ApiResponse<TaskDto>> UpdateTaskProgressAsync(long taskId, int progress, string? remarks, int currentUserId)
        {
            try
            {
                var task = await _taskRepository.GetTaskByIdAsync(taskId);
                if (task == null)
                {
                    return ApiResponse<TaskDto>.CreateFail($"未找到任务: ID={taskId}");
                }

                var oldProgress = task.Progress;
                task.Progress = progress;
                if (progress == 100 && (task.Status != "Done" && task.Status != "Completed") && task.ActualEndDate == null)
                {
                     _logger.LogInformation("任务 {TaskId} 进度达到100%，但状态未自动变更为完成。当前状态: {CurrentStatus}", taskId, task.Status);
                }
                task.LastUpdatedTimestamp = DateTime.Now;
                await _taskRepository.UpdateTaskAsync(task);
                
                var history = new TaskHistory
                {
                    TaskId = taskId,
                    UserId = currentUserId,
                    Timestamp = DateTime.Now,
                    ActionType = "ProgressUpdate",
                    OldValue = oldProgress.ToString(),
                    NewValue = progress.ToString(),
                    Description = remarks 
                };
                await _taskRepository.AddTaskHistoryAsync(history);

                var updatedTaskDto = await MapTaskToDtoAsync(task);
                return ApiResponse<TaskDto>.CreateSuccess(updatedTaskDto!, "任务进度更新成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新任务进度时发生错误: TaskId={TaskId}", taskId);
                return ApiResponse<TaskDto>.CreateFail("更新任务进度时发生错误: " + ex.Message);
            }
        }

        /// <summary>
        /// 分配任务给指定用户
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="assigneeUserId">负责人用户ID</param>
        /// <param name="remarks">备注</param>
        /// <param name="currentUserId">当前用户ID</param>
        /// <returns>任务DTO</returns>
        public async Task<ApiResponse<TaskDto>> AssignTaskAsync(long taskId, int assigneeUserId, string? remarks, int currentUserId)
        {
            try
            {
                var task = await _taskRepository.GetTaskByIdAsync(taskId);
                if (task == null)
                {
                    return ApiResponse<TaskDto>.CreateFail($"未找到任务: ID={taskId}");
                }

                // 验证指定的用户是否存在
                if (!await _coreDataQueryService.UserExistsAsync(assigneeUserId))
                {
                    return ApiResponse<TaskDto>.CreateFail($"指定的用户不存在: ID={assigneeUserId}");
                }

                // 记录旧的负责人ID用于通知
                var oldAssigneeId = task.AssigneeUserId;
                var oldAssigneeIds = new List<int>();
                if (oldAssigneeId.HasValue)
                {
                    oldAssigneeIds.Add(oldAssigneeId.Value);
                }
                
                // 获取当前任务的所有协作者
                var taskAssignees = await _taskRepository.GetAssigneesByTaskIdAsync(taskId);
                foreach (var assignee in taskAssignees)
                {
                    if (assignee.UserId != oldAssigneeId)
                    {
                        oldAssigneeIds.Add(assignee.UserId);
                    }
                }

                // 更新任务负责人
                task.AssigneeUserId = assigneeUserId;
                task.LastUpdatedTimestamp = DateTime.Now;
                await _taskRepository.UpdateTaskAsync(task);

                // 创建任务历史记录
                var assigneeUser = await _coreDataQueryService.GetUserAsync(assigneeUserId);
                string assigneeName = assigneeUser?.Username ?? $"用户ID:{assigneeUserId}";
                
                await _taskRepository.AddTaskHistoryAsync(new TaskHistory
                {
                    TaskId = taskId,
                    UserId = currentUserId,
                    Timestamp = DateTime.Now,
                    ActionType = "TaskAssigned",
                    FieldName = "AssigneeUserId",
                    OldValue = oldAssigneeId?.ToString() ?? "无",
                    NewValue = assigneeUserId.ToString(),
                    Description = remarks ?? $"任务分配给 {assigneeName}"
                });

                // 发送通知
                var newAssigneeIds = new List<int> { assigneeUserId };
                await _notificationService.NotifyTaskAssigneeChangedAsync(taskId, oldAssigneeIds, newAssigneeIds, currentUserId);

                var updatedTaskDto = await MapTaskToDtoAsync(task);
                return ApiResponse<TaskDto>.CreateSuccess(updatedTaskDto!, "任务已成功分配");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "分配任务时发生错误: TaskId={TaskId}, AssigneeId={AssigneeId}", taskId, assigneeUserId);
                return ApiResponse<TaskDto>.CreateFail("分配任务时发生错误: " + ex.Message);
            }
        }

        /// <summary>
        /// 完成任务
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="remarks">备注</param>
        /// <param name="currentUserId">当前用户ID</param>
        /// <returns>任务DTO</returns>
        public async Task<ApiResponse<TaskDto>> CompleteTaskAsync(long taskId, string? remarks, int currentUserId)
        {
            try
            {
                var task = await _taskRepository.GetTaskByIdAsync(taskId);
                if (task == null)
                {
                    return ApiResponse<TaskDto>.CreateFail($"未找到任务: ID={taskId}");
                }

                if ("Completed".Equals(task.Status, StringComparison.OrdinalIgnoreCase) || "Done".Equals(task.Status, StringComparison.OrdinalIgnoreCase))
                {
                    var currentTaskDto = await MapTaskToDtoAsync(task);
                    return ApiResponse<TaskDto>.CreateSuccess(currentTaskDto!, "任务已完成");
                }

                string oldStatus = task.Status;
                
                task.Status = "Completed"; 
                task.ActualEndDate = DateTime.Now; 
                task.Progress = 100;
                task.LastUpdatedTimestamp = DateTime.Now; 

                await _taskRepository.UpdateTaskAsync(task);

                await _taskRepository.AddTaskHistoryAsync(new TaskHistory
                {
                    TaskId = taskId,
                    UserId = currentUserId,
                    Timestamp = DateTime.Now,
                    ActionType = "CompleteTask",
                    FieldName = "Status",
                    OldValue = oldStatus,
                    NewValue = task.Status,
                    Description = remarks 
                });

                // 发送任务状态变更通知
                await _notificationService.NotifyTaskStatusChangedAsync(taskId, oldStatus, "Completed", currentUserId);

                // Convert string task.TaskType to Models.Enums.TaskType for the event
                if (!System.Enum.TryParse<CoreTaskTypeEnum>(task.TaskType, true, out var coreTaskTypeEnum))
                {
                    _logger.LogWarning("无法将任务类型字符串 '{TaskTypeString}' 解析为枚举 CoreTaskTypeEnum. TaskId: {TaskId}", task.TaskType, task.TaskId);
                    // Fallback or handle error appropriately, e.g., use a default or skip event if critical
                    coreTaskTypeEnum = CoreTaskTypeEnum.Regular; // Changed Normal to Regular
                }

                await _mediator.Publish(new TaskCompletedEvent(
                    task.TaskId,
                    task.Name,
                    task.CreatorUserId,
                    task.AssigneeUserId,
                    currentUserId, // completerUserId
                    task.ActualEndDate ?? DateTime.Now, // completedTimestamp
                    coreTaskTypeEnum, // coreTaskType (enum)
                    task.Points      // points
                ));

                // 性能优化：清除任务列表缓存 (任务完成后需要刷新列表)
                await _taskCacheService.InvalidateTaskCacheAsync(taskId);
                _logger.LogDebug("任务 {TaskId} 完成后已清除任务列表缓存", taskId);

                var completedTaskDto = await MapTaskToDtoAsync(task);
                return ApiResponse<TaskDto>.CreateSuccess(completedTaskDto!, "任务已成功标记为完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "完成任务时发生错误: ID={TaskId}", taskId);
                return ApiResponse<TaskDto>.CreateFail("完成任务时发生错误: " + ex.Message);
            }
        }
        
        /// <summary>
        /// 获取任务评论列表
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>评论列表</returns>
        public async Task<ApiResponse<List<CommentDto>>> GetTaskCommentsAsync(long taskId)
        {
            try
            {
                if (!await _taskRepository.TaskExistsAsync(taskId))
                {
                    return ApiResponse<List<CommentDto>>.CreateFail($"未找到指定的任务: ID={taskId}");
                }

                var comments = await _taskRepository.GetCommentsByTaskIdAsync(taskId);
                var commentDtos = new List<CommentDto>();
                foreach (var comment in comments.OrderBy(c => c.CreationTimestamp)) 
                {
                    var user = await _coreDataQueryService.GetUserAsync(comment.UserId);
                    commentDtos.Add(new CommentDto
                    {
                        CommentId = comment.CommentId,
                        TaskId = comment.TaskId,
                        Content = comment.Content,
                        UserId = comment.UserId,
                        UserName = user?.Username ?? "未知用户",
                        UserAvatarUrl = user?.AvatarUrl,
                        CreationTimestamp = comment.CreationTimestamp,
                        LastUpdatedTimestamp = comment.LastUpdatedTimestamp,
                        ParentCommentId = comment.ParentCommentId,
                        IsPinned = comment.IsPinned,
                        IsEdited = comment.IsEdited,
                        MentionedUserIds = comment.MentionedUserIds // Directly assign the string
                    });
                }
                return ApiResponse<List<CommentDto>>.CreateSuccess(commentDtos, "获取评论列表成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务评论时发生错误: TaskId={TaskId}", taskId);
                return ApiResponse<List<CommentDto>>.CreateFail("获取任务评论时发生错误: " + ex.Message);
            }
        }

        /// <summary>
        /// 比较两个列表是否相等
        /// </summary>
        private bool AreListsEqual<T>(List<T> list1, List<T> list2)
        {
            if (list1 == null && list2 == null)
                return true;
            
            if (list1 == null || list2 == null)
                return false;
                
            if (list1.Count != list2.Count)
                return false;
                
            return list1.OrderBy(x => x).SequenceEqual(list2.OrderBy(x => x));
        }

        /// <summary>
        /// 添加任务评论
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="request">评论请求</param>
        /// <param name="currentUserId">当前用户ID</param>
        /// <returns>添加的评论</returns>
        public async Task<ApiResponse<CommentDto>> AddCommentAsync(long taskId, AddCommentRequestDto request, int currentUserId)
        {
            _logger.LogInformation("Attempting to add comment for task {TaskId} by user {UserId}", taskId, currentUserId);
            try
            {
                if (!await _taskRepository.TaskExistsAsync(taskId)) // Check if task exists
                {
                    return ApiResponse<CommentDto>.CreateFail($"未找到指定的任务: ID={taskId}");
                }

                if (request.ParentCommentId.HasValue && !await _taskRepository.CommentExistsAsync(request.ParentCommentId.Value, taskId))
                {
                    return ApiResponse<CommentDto>.CreateFail($"指定的父评论不存在或不属于该任务: ParentCommentId={request.ParentCommentId.Value}");
                }

                string? mentionedUserIdsJson = null;
                if (request.MentionedUserIds != null && request.MentionedUserIds.Any())
                {
                    mentionedUserIdsJson = JsonSerializer.Serialize(request.MentionedUserIds);
                }

                var comment = new Comment
                {
                    TaskId = taskId,
                    UserId = currentUserId,
                    Content = request.Content,
                    CreationTimestamp = DateTime.Now,
                    LastUpdatedTimestamp = DateTime.Now,
                    ParentCommentId = request.ParentCommentId,
                    MentionedUserIds = mentionedUserIdsJson // Assign serialized JSON string
                };

                var createdComment = await _taskRepository.AddCommentAsync(comment);
                await _taskRepository.AddTaskHistoryAsync(new TaskHistory
                {
                    TaskId = taskId,
                    UserId = currentUserId,
                    Timestamp = DateTime.Now,
                    ActionType = "AddComment",
                    Description = $"添加了评论: {createdComment.Content.Substring(0, Math.Min(createdComment.Content.Length, 50))}...",
                    CommentId = createdComment.CommentId 
                });
                
                // 发送评论通知
                await _notificationService.NotifyTaskCommentAsync(taskId, createdComment.CommentId, currentUserId, createdComment.Content);
                
                // 如果有提及用户，发送提及通知
                if (request.MentionedUserIds != null && request.MentionedUserIds.Any())
                {
                    await _notificationService.NotifyTaskMentionAsync(taskId, request.MentionedUserIds, currentUserId, createdComment.Content);
                }
                
                if (createdComment.CommentId > 0) 
                {
                    var task = await _taskRepository.GetTaskByIdAsync(taskId); // Need task title for event
                    if (task != null) {
                         await _mediator.Publish(new CommentAddedEvent(
                             createdComment.CommentId, 
                             taskId, 
                             task.Name, // taskTitle
                             currentUserId, 
                             createdComment.Content ?? string.Empty, 
                             createdComment.CreationTimestamp, // createdAt
                             createdComment.MentionedUserIds ?? string.Empty // mentionedUserIds (string)
                         ));
                    }
                }

                var user = await _coreDataQueryService.GetUserAsync(currentUserId);
                var commentDto = new CommentDto
                {
                    CommentId = createdComment.CommentId,
                    TaskId = createdComment.TaskId,
                    Content = createdComment.Content,
                    UserId = createdComment.UserId,
                    UserName = user?.Username ?? "未知用户",
                    UserAvatarUrl = user?.AvatarUrl,
                    CreationTimestamp = createdComment.CreationTimestamp,
                    LastUpdatedTimestamp = createdComment.LastUpdatedTimestamp,
                    ParentCommentId = createdComment.ParentCommentId,
                    MentionedUserIds = createdComment.MentionedUserIds // Assign string from createdComment
                };
                return ApiResponse<CommentDto>.CreateSuccess(commentDto, "评论添加成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加评论时发生错误: TaskId={TaskId}", taskId);
                return ApiResponse<CommentDto>.CreateFail("添加评论时发生错误: " + ex.Message);
            }
        }

        /// <summary>
        /// 获取任务附件列表
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>附件列表</returns>
        public async Task<ApiResponse<List<AttachmentDto>>> GetTaskAttachmentsAsync(long taskId)
        {
            try
            {
                if (!await _taskRepository.TaskExistsAsync(taskId))
                {
                    return ApiResponse<List<AttachmentDto>>.CreateFail($"未找到指定的任务: ID={taskId}");
                }

                var attachments = await _taskRepository.GetAttachmentsByTaskIdAsync(taskId);
                var attachmentDtos = new List<AttachmentDto>();
                foreach (var attachment in attachments)
                {
                    var uploader = await _coreDataQueryService.GetUserAsync(attachment.UploaderUserId);
                    attachmentDtos.Add(new AttachmentDto
                    {
                        AttachmentId = attachment.AttachmentId,
                        TaskId = attachment.TaskId,
                        FileName = attachment.FileName,
                        FilePath = attachment.FilePath, 
                        FileSize = attachment.FileSize,
                        FileType = attachment.FileType,
                        CreationTimestamp = attachment.CreationTimestamp,
                        UploaderUserId = attachment.UploaderUserId,
                        UploaderUserName = uploader?.Username ?? "未知用户",
                        StoredFileName = attachment.StoredFileName,
                        IsPreviewable = attachment.IsPreviewable,
                        StorageType = attachment.StorageType
                    });
                }
                return ApiResponse<List<AttachmentDto>>.CreateSuccess(attachmentDtos, "获取附件列表成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务附件时发生错误: TaskId={TaskId}", taskId);
                return ApiResponse<List<AttachmentDto>>.CreateFail("获取任务附件时发生错误: " + ex.Message);
            }
        }

        /// <summary>
        /// 获取任务历史记录
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>历史记录列表</returns>
        public async Task<ApiResponse<List<TaskHistoryDto>>> GetTaskHistoryAsync(long taskId)
        {
            try
            {
                if (!await _taskRepository.TaskExistsAsync(taskId))
                {
                    return ApiResponse<List<TaskHistoryDto>>.CreateFail($"未找到指定的任务: ID={taskId}");
                }

                var historyEntries = await _taskRepository.GetHistoryByTaskIdAsync(taskId);
                var historyDtos = new List<TaskHistoryDto>();
                foreach (var entry in historyEntries.OrderByDescending(h => h.Timestamp)) 
                {
                    var user = entry.UserId.HasValue ? await _coreDataQueryService.GetUserAsync(entry.UserId.Value) : null;
                    historyDtos.Add(new TaskHistoryDto
                    {
                        TaskHistoryId = entry.TaskHistoryId,
                        TaskId = entry.TaskId,
                        UserId = entry.UserId,
                        UserName = user?.Username ?? "系统操作",
                        UserAvatarUrl = user?.AvatarUrl,
                        Timestamp = entry.Timestamp,
                        ActionType = entry.ActionType,
                        FieldName = entry.FieldName,
                        OldValue = entry.OldValue,
                        NewValue = entry.NewValue,
                        Description = entry.Description,
                        CommentId = entry.CommentId,
                        AttachmentId = entry.AttachmentId
                    });
                }
                return ApiResponse<List<TaskHistoryDto>>.CreateSuccess(historyDtos, "获取历史记录成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务历史记录时发生错误: TaskId={TaskId}", taskId);
                return ApiResponse<List<TaskHistoryDto>>.CreateFail("获取任务历史记录时发生错误: " + ex.Message);
            }
        }

        public async Task<ApiResponse<AttachmentDto>> AddAttachmentAsync(long taskId, string fileName, byte[] fileContent, string contentType, string? descriptionForHistory, int uploaderUserId)
        {
            try
            {
                if (!await _taskRepository.TaskExistsAsync(taskId)) // Check if task exists
                {
                    return ApiResponse<AttachmentDto>.CreateFail($"未找到任务: ID={taskId}");
                }

                string uniqueFileName = $"{Guid.NewGuid().ToString().Substring(0,8)}_{fileName}"; 
                // Assuming _fileStorageService.SaveFileAsync returns relative path for FilePath
                string? relativePath = await _fileStorageService.SaveFileAsync(fileContent, uniqueFileName, $"tasks/{taskId}"); 

                if (string.IsNullOrEmpty(relativePath))
                {
                    _logger.LogError("文件存储失败: SaveFileAsync for {FileName} in tasks/{TaskId} returned null or empty path.", uniqueFileName, taskId);
                    return ApiResponse<AttachmentDto>.CreateFail("文件存储失败，无法获取有效的文件路径。请检查存储服务配置或磁盘空间。");
                }

                var attachment = new Attachment
                {
                    TaskId = taskId,
                    FileName = fileName,
                    StoredFileName = uniqueFileName, // Store the unique name
                    FilePath = relativePath, // Now safe, as relativePath is checked
                    FileSize = fileContent.Length,
                    FileType = contentType, // Corrected: Assign to FileType
                    UploaderUserId = uploaderUserId,
                    CreationTimestamp = DateTime.Now, // Corrected: Assign to CreationTimestamp
                    IsPreviewable = CanPreview(contentType) // Add a helper to determine this
                };

                var createdAttachment = await _taskRepository.AddAttachmentAsync(attachment);

                await _taskRepository.AddTaskHistoryAsync(new TaskHistory
                {
                    TaskId = taskId,
                    UserId = uploaderUserId,
                    Timestamp = DateTime.Now,
                    ActionType = "AddAttachment",
                    Description = descriptionForHistory ?? $"添加了附件: {fileName}",
                    AttachmentId = createdAttachment.AttachmentId 
                });
                
                // 发送附件添加通知
                await _notificationService.NotifyTaskAttachmentAddedAsync(taskId, createdAttachment.AttachmentId, uploaderUserId, fileName);
                
                if (createdAttachment.AttachmentId > 0)
                {
                    var task = await _taskRepository.GetTaskByIdAsync(taskId); // Need task title for event
                    if (task != null) {
                        await _mediator.Publish(new AttachmentAddedEvent(
                            createdAttachment.AttachmentId, 
                            taskId, 
                            task.Name, // taskTitle
                            uploaderUserId, 
                            createdAttachment.FileName, 
                            createdAttachment.FileSize, 
                            createdAttachment.CreationTimestamp // uploadedAt
                        ));
                    }
                }

                var uploader = await _coreDataQueryService.GetUserAsync(uploaderUserId); 
                var attachmentDto = new AttachmentDto
                {
                    AttachmentId = createdAttachment.AttachmentId,
                    TaskId = createdAttachment.TaskId,
                    FileName = createdAttachment.FileName,
                    FilePath = createdAttachment.FilePath, 
                    FileSize = createdAttachment.FileSize,
                    FileType = createdAttachment.FileType, // Corrected: Use FileType
                    CreationTimestamp = createdAttachment.CreationTimestamp, // Corrected: Use CreationTimestamp
                    UploaderUserId = createdAttachment.UploaderUserId,
                    UploaderUserName = uploader?.Username ?? "未知用户",
                    StoredFileName = createdAttachment.StoredFileName,
                    IsPreviewable = createdAttachment.IsPreviewable,
                    StorageType = createdAttachment.StorageType
                };

                return ApiResponse<AttachmentDto>.CreateSuccess(attachmentDto, "附件上传成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "上传附件时发生错误: TaskId={TaskId}", taskId);
                return ApiResponse<AttachmentDto>.CreateFail("附件上传失败: " + ex.Message);
            }
        }

        private bool CanPreview(string contentType)
        {
            // Basic implementation, can be expanded
            var previewableTypes = new List<string> { "image/jpeg", "image/png", "image/gif", "application/pdf", "text/plain" };
            return previewableTypes.Contains(contentType.ToLower());
        }

        public async Task<ApiResponse<bool>> DeleteAttachmentAsync(long attachmentId, int currentUserId)
        {
            _logger.LogInformation("Attempting to delete attachment {AttachmentId} by user {UserId}", attachmentId, currentUserId);
            try
            {
                var attachment = await _taskRepository.GetAttachmentByIdAsync(attachmentId);
                if (attachment == null)
                {
                    return ApiResponse<bool>.CreateFail($"未找到附件: ID={attachmentId}");
                }

                // Optional: Check if currentUserId has permission to delete this attachment
                // e.g., if(attachment.UploaderUserId != currentUserId && !await _userService.IsAdmin(currentUserId)) { ... }

                await _taskRepository.DeleteAttachmentAsync(attachmentId);
                // Attempt to delete the physical file
                await _fileStorageService.DeleteFileAsync(attachment.FilePath);

                await _taskRepository.AddTaskHistoryAsync(new TaskHistory
                {
                    TaskId = attachment.TaskId.HasValue ? attachment.TaskId.Value : 0, // 添加检查和安全转换
                    UserId = currentUserId,
                    Timestamp = DateTime.Now,
                    ActionType = "DeleteAttachment",
                    Description = $"删除了附件: {attachment.FileName}",
                    AttachmentId = attachmentId
                });

                // Publish AttachmentDeletedEvent
                if (attachment.TaskId.HasValue) // Only publish if linked to a task
                {
                     await _mediator.Publish(new AttachmentDeletedEvent(attachmentId, attachment.TaskId.Value, attachment.FileName, currentUserId));
                }

                return ApiResponse<bool>.CreateSuccess(true, "附件删除成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除附件时发生错误: AttachmentId={AttachmentId}", attachmentId);
                return ApiResponse<bool>.CreateFail("删除附件时发生错误: " + ex.Message);
            }
        }

        /// <summary>
        /// 获取任务DTO
        /// </summary>
        /// <param name="taskEntity">任务实体</param>
        /// <returns>任务DTO</returns>
        private async Task<TaskDto?> MapTaskToDtoAsync(TaskEntity? taskEntity)
        {
            if (taskEntity == null) return null;

            var dto = new TaskDto
            {
                TaskId = taskEntity.TaskId,
                Name = taskEntity.Name,
                Description = taskEntity.Description,
                Status = taskEntity.Status,
                StatusName = GetStatusName(taskEntity.Status), // 新增：状态显示名称
                Priority = taskEntity.Priority,
                PriorityText = GetPriorityText(taskEntity.Priority), // 新增：优先级显示名称
                TaskType = taskEntity.TaskType,
                CreationTimestamp = taskEntity.CreationTimestamp,
                ActualEndDate = taskEntity.ActualEndDate,
                PlanEndDate = taskEntity.PlanEndDate,
                PlanStartDate = taskEntity.PlanStartDate,
                ActualStartDate = taskEntity.ActualStartDate,
                Progress = taskEntity.Progress,
                AssigneeUserId = taskEntity.AssigneeUserId,
                CreatorUserId = taskEntity.CreatorUserId,
                AssetId = taskEntity.AssetId,
                LocationId = taskEntity.LocationId,
                ParentTaskId = taskEntity.ParentTaskId,
                PDCAStage = taskEntity.PDCAStage,
                Points = taskEntity.Points,
                LastUpdatedTimestamp = taskEntity.LastUpdatedTimestamp,
                PeriodicTaskScheduleId = taskEntity.PeriodicTaskScheduleId,
                ProjectId = taskEntity.ProjectId,
                IsOverdue = (taskEntity.PlanEndDate.HasValue && taskEntity.PlanEndDate.Value < DateTime.Now &&
                               !("Completed".Equals(taskEntity.Status, StringComparison.OrdinalIgnoreCase) ||
                                 "Done".Equals(taskEntity.Status, StringComparison.OrdinalIgnoreCase) ||
                                 "Canceled".Equals(taskEntity.Status, StringComparison.OrdinalIgnoreCase) ||
                                 "Archived".Equals(taskEntity.Status, StringComparison.OrdinalIgnoreCase)))
            };

            // Populate Assignee User Info
            if (taskEntity.AssigneeUserId.HasValue)
            {
                var assignee = await _coreDataQueryService.GetUserAsync(taskEntity.AssigneeUserId.Value);
                if (assignee != null)
                {
                    dto.AssigneeUserName = assignee.Username;
                    dto.AssigneeAvatarUrl = assignee.AvatarUrl;
                }
            }

            // Populate Creator User Info
            if (taskEntity.CreatorUserId > 0)
            {
                var creator = await _coreDataQueryService.GetUserAsync(taskEntity.CreatorUserId);
                if (creator != null)
                {
                    dto.CreatorUserName = creator.Username;
                    dto.CreatorUserAvatarUrl = creator.AvatarUrl; 
                }
            }
            
            // Populate Parent Task Name
            if (taskEntity.ParentTaskId.HasValue)
            {
                var parentTask = await _taskRepository.GetTaskByIdAsync(taskEntity.ParentTaskId.Value);
                if (parentTask != null)
                {
                    dto.ParentTaskName = parentTask.Name;
                }
            }
            
            // Populate Asset Info
            if(taskEntity.AssetId.HasValue)
            {
                var asset = await _coreDataQueryService.GetAssetAsync(taskEntity.AssetId.Value);
                if(asset != null)
                {
                    dto.AssetName = asset.Name;
                    dto.AssetNumber = asset.AssetNumber;
                }
            }

            // Populate Location Info
            if(taskEntity.LocationId.HasValue)
            {
                var location = await _coreDataQueryService.GetLocationAsync(taskEntity.LocationId.Value);
                if(location != null)
                {
                    dto.LocationName = location.Name;
                }
            }
            
            // Populate Schedule Info
            if(taskEntity.PeriodicTaskScheduleId.HasValue)
            {
                 var scheduleName = await _taskRepository.GetPeriodicTaskScheduleNameByIdAsync(taskEntity.PeriodicTaskScheduleId.Value);
                 dto.PeriodicScheduleName = scheduleName;
            }

            dto.SubTaskCount = await _taskRepository.GetSubTaskCountAsync(taskEntity.TaskId);
            dto.CommentCount = await _taskRepository.GetCommentCountAsync(taskEntity.TaskId);
            dto.AttachmentCount = await _taskRepository.GetAttachmentCountAsync(taskEntity.TaskId);

            // Map Comments
            if (taskEntity.Comments != null && taskEntity.Comments.Any())
            {
                dto.Comments = new List<CommentDto>();
                foreach (var commentEntity in taskEntity.Comments.OrderBy(c => c.CreationTimestamp))
                {
                    var commentDto = new CommentDto
                    {
                        CommentId = commentEntity.CommentId,
                        TaskId = commentEntity.TaskId,
                        Content = commentEntity.Content,
                        UserId = commentEntity.UserId,
                        CreationTimestamp = commentEntity.CreationTimestamp,
                        LastUpdatedTimestamp = commentEntity.LastUpdatedTimestamp,
                        ParentCommentId = commentEntity.ParentCommentId,
                        IsPinned = commentEntity.IsPinned,
                        IsEdited = commentEntity.IsEdited,
                        MentionedUserIds = commentEntity.MentionedUserIds
                    };
                    var commentUser = await _coreDataQueryService.GetUserAsync(commentEntity.UserId);
                    if (commentUser != null)
                    {
                        commentDto.UserName = commentUser.Username;
                        commentDto.UserAvatarUrl = commentUser.AvatarUrl;
                    }
                    dto.Comments.Add(commentDto);
                }
            }

            // Map Attachments
            if (taskEntity.Attachments != null && taskEntity.Attachments.Any())
            {
                dto.Attachments = new List<AttachmentDto>();
                foreach (var attachmentEntity in taskEntity.Attachments.OrderByDescending(a => a.CreationTimestamp))
                {
                    var attachmentDto = new AttachmentDto
                    {
                        AttachmentId = attachmentEntity.AttachmentId,
                        TaskId = attachmentEntity.TaskId,
                        CommentId = attachmentEntity.CommentId,
                        UploaderUserId = attachmentEntity.UploaderUserId,
                        FileName = attachmentEntity.FileName,
                        StoredFileName = attachmentEntity.StoredFileName,
                        FilePath = attachmentEntity.FilePath, 
                        FileSize = attachmentEntity.FileSize,
                        FileType = attachmentEntity.FileType,
                        IsPreviewable = attachmentEntity.IsPreviewable,
                        StorageType = attachmentEntity.StorageType,
                        CreationTimestamp = attachmentEntity.CreationTimestamp,
                        // Description = attachmentEntity.Description, // Removed: Not on V2 Attachment entity
                        // ThumbnailPath = attachmentEntity.ThumbnailPath, // Removed: Not on V2 Attachment entity
                        // IsDeleted = attachmentEntity.IsDeleted // Removed: Not on V2 Attachment entity
                    };
                    var uploader = await _coreDataQueryService.GetUserAsync(attachmentEntity.UploaderUserId);
                    if (uploader != null)
                    {
                        attachmentDto.UploaderUserName = uploader.Username; // Or Name
                        attachmentDto.UploaderAvatarUrl = uploader.AvatarUrl;
                    }
                    dto.Attachments.Add(attachmentDto);
                }
            }

            // Map History
            if (taskEntity.History != null && taskEntity.History.Any())
            {
                dto.History = new List<TaskHistoryDto>();
                foreach (var historyEntity in taskEntity.History.OrderByDescending(h => h.Timestamp))
                {
                    var historyDto = new TaskHistoryDto
                    {
                        TaskHistoryId = historyEntity.TaskHistoryId,
                        TaskId = historyEntity.TaskId,
                        UserId = historyEntity.UserId,
                        Timestamp = historyEntity.Timestamp,
                        ActionType = historyEntity.ActionType,
                        FieldName = historyEntity.FieldName,
                        OldValue = historyEntity.OldValue,
                        NewValue = historyEntity.NewValue,
                        Description = historyEntity.Description, 
                        CommentId = historyEntity.CommentId,
                        AttachmentId = historyEntity.AttachmentId
                        // IpAddress = historyEntity.IpAddress, // Removed: Not on V2 TaskHistory entity
                        // UserAgent = historyEntity.UserAgent  // Removed: Not on V2 TaskHistory entity
                    };
                    if (historyEntity.UserId.HasValue)
                    {
                        var historyUser = await _coreDataQueryService.GetUserAsync(historyEntity.UserId.Value);
                        if (historyUser != null)
                        {
                            historyDto.UserName = historyUser.Username; // Or Name
                            historyDto.UserAvatarUrl = historyUser.AvatarUrl;
                        }
                    }
                    dto.History.Add(historyDto);
                }
            }
            
            // Map TaskAssignees to both Assignees and Participants DTO lists
            if (taskEntity.Assignees != null && taskEntity.Assignees.Any())
            {
                dto.Assignees = new List<AssigneeDto>();
                dto.Participants = new List<UserBasicDto>();
                
                foreach (var assigneeEntry in taskEntity.Assignees)
                {
                    try 
                {
                    var user = await _coreDataQueryService.GetUserAsync(assigneeEntry.UserId);
                    if (user != null)
                    {
                            // 添加到Assignees列表（完整信息）
                            var assigneeDto = new AssigneeDto
                            {
                                UserId = user.Id,
                                UserName = user.Name ?? user.Username ?? "未知用户",
                                AvatarUrl = string.IsNullOrEmpty(user.AvatarUrl) ? "" : user.AvatarUrl,
                                AssignmentType = assigneeEntry.AssignmentType ?? "Participant",
                                Role = assigneeEntry.UserId == taskEntity.AssigneeUserId ? "Primary" : "Collaborator",
                                AssignmentTimestamp = assigneeEntry.AssignmentTimestamp
                            };
                            dto.Assignees.Add(assigneeDto);
                            
                            // 添加到Participants列表（向后兼容）
                            dto.Participants.Add(new UserBasicDto 
                            { 
                                Id = user.Id, 
                                Name = user.Name ?? user.Username ?? "未知用户",
                                AvatarUrl = string.IsNullOrEmpty(user.AvatarUrl) ? "" : user.AvatarUrl
                            });
                            
                            _logger.LogDebug("已映射任务{TaskId}的负责人: UserId={UserId}, Name={Name}, Role={Role}, AvatarUrl={AvatarUrl}", 
                                taskEntity.TaskId, user.Id, user.Name, assigneeDto.Role, user.AvatarUrl);
                        }
                        else
                        {
                            _logger.LogWarning("获取任务{TaskId}的负责人用户信息失败: UserId={UserId}", taskEntity.TaskId, assigneeEntry.UserId);
                            
                            // 添加最基本的信息，避免UI显示问题
                            var assigneeDto = new AssigneeDto
                            {
                                UserId = assigneeEntry.UserId,
                                UserName = "未知用户",
                                AvatarUrl = "",
                                AssignmentType = assigneeEntry.AssignmentType ?? "Participant",
                                Role = assigneeEntry.UserId == taskEntity.AssigneeUserId ? "Primary" : "Collaborator",
                                AssignmentTimestamp = assigneeEntry.AssignmentTimestamp
                            };
                            dto.Assignees.Add(assigneeDto);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "映射任务{TaskId}的负责人时出错: UserId={UserId}", taskEntity.TaskId, assigneeEntry.UserId);
                    }
                }
                
                _logger.LogInformation("任务{TaskId}映射了{Count}个负责人/协作者", taskEntity.TaskId, dto.Assignees.Count);
            }
            else
            {
                _logger.LogWarning("任务{TaskId}没有Assignees集合数据，可能影响前端显示", taskEntity.TaskId);
                dto.Assignees = new List<AssigneeDto>();
                dto.Participants = new List<UserBasicDto>();
            }
            
            // 如果主负责人不在TaskAssignees中，添加主负责人到Assignees列表
            if (taskEntity.AssigneeUserId.HasValue && 
                (dto.Assignees == null || !dto.Assignees.Any(a => a.UserId == taskEntity.AssigneeUserId.Value)))
            {
                try 
                {
                    var mainAssigneeUser = await _coreDataQueryService.GetUserAsync(taskEntity.AssigneeUserId.Value);
                    if (mainAssigneeUser != null)
                    {
                        if (dto.Assignees == null) dto.Assignees = new List<AssigneeDto>();
                        
                        var mainAssigneeDto = new AssigneeDto
                        {
                            UserId = mainAssigneeUser.Id,
                            UserName = mainAssigneeUser.Name ?? mainAssigneeUser.Username ?? "未知用户",
                            AvatarUrl = string.IsNullOrEmpty(mainAssigneeUser.AvatarUrl) ? "" : mainAssigneeUser.AvatarUrl,
                            AssignmentType = "Assignee",
                            Role = "Primary",
                            AssignmentTimestamp = taskEntity.CreationTimestamp // 默认使用任务创建时间
                        };
                        dto.Assignees.Add(mainAssigneeDto);
                        
                        if (dto.Participants == null) dto.Participants = new List<UserBasicDto>();
                        dto.Participants.Add(new UserBasicDto 
                        { 
                            Id = mainAssigneeUser.Id, 
                            Name = mainAssigneeUser.Name ?? mainAssigneeUser.Username ?? "未知用户",
                            AvatarUrl = string.IsNullOrEmpty(mainAssigneeUser.AvatarUrl) ? "" : mainAssigneeUser.AvatarUrl
                        });
                        
                        _logger.LogInformation("为任务{TaskId}添加了缺失的主负责人: UserId={UserId}, Name={Name}", 
                            taskEntity.TaskId, mainAssigneeUser.Id, mainAssigneeUser.Name);
                    }
                    else
                    {
                        _logger.LogWarning("无法获取任务{TaskId}的主负责人信息: AssigneeUserId={UserId}", 
                            taskEntity.TaskId, taskEntity.AssigneeUserId.Value);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "为任务{TaskId}添加主负责人时出错: AssigneeUserId={UserId}", 
                        taskEntity.TaskId, taskEntity.AssigneeUserId.Value);
                }
            }

            return dto;
        }

        /// <summary>
        /// 批量映射任务DTO，优化性能避免N+1查询
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <returns>任务DTO列表</returns>
        private async Task<List<TaskDto>> MapTasksToDtosOptimizedAsync(List<TaskEntity> tasks)
        {
            if (!tasks.Any()) return new List<TaskDto>();

            _logger.LogInformation("开始优化批量映射{Count}个任务", tasks.Count);

            // 收集所有需要查询的用户ID
            var userIds = new HashSet<int>();
            var assetIds = new HashSet<int>();
            var locationIds = new HashSet<int>();
            var parentTaskIds = new HashSet<long>();
            var taskIds = tasks.Select(t => t.TaskId).ToList();

            foreach (var task in tasks)
            {
                if (task.AssigneeUserId.HasValue) userIds.Add(task.AssigneeUserId.Value);
                if (task.CreatorUserId > 0) userIds.Add(task.CreatorUserId);
                if (task.AssetId.HasValue) assetIds.Add(task.AssetId.Value);
                if (task.LocationId.HasValue) locationIds.Add(task.LocationId.Value);
                if (task.ParentTaskId.HasValue) parentTaskIds.Add(task.ParentTaskId.Value);
                
                // 收集评论和附件中的用户ID
                if (task.Comments != null)
                {
                    foreach (var comment in task.Comments)
                    {
                        userIds.Add(comment.UserId);
                    }
                }
                if (task.Attachments != null)
                {
                    foreach (var attachment in task.Attachments)
                    {
                        userIds.Add(attachment.UploaderUserId);
                    }
                }
                if (task.History != null)
                {
                    foreach (var history in task.History)
                    {
                        if (history.UserId.HasValue) userIds.Add(history.UserId.Value);
                    }
                }
                if (task.Assignees != null)
                {
                    foreach (var assignee in task.Assignees)
                    {
                        userIds.Add(assignee.UserId);
                    }
                }
            }

            _logger.LogInformation("收集到的ID统计 - 用户:{UserCount}, 资产:{AssetCount}, 位置:{LocationCount}, 父任务:{ParentTaskCount}", 
                userIds.Count, assetIds.Count, locationIds.Count, parentTaskIds.Count);

            // 批量查询所有相关数据
            var usersTask = userIds.Any() ? _coreDataQueryService.GetUsersAsync(userIds.ToList()) : System.Threading.Tasks.Task.FromResult(new List<CoreUserDto>());
            var assetsTask = assetIds.Any() ? _coreDataQueryService.GetAssetsAsync(assetIds.ToList()) : System.Threading.Tasks.Task.FromResult(new List<CoreAssetDto>());
            var locationsTask = locationIds.Any() ? _coreDataQueryService.GetLocationsAsync(locationIds.ToList()) : System.Threading.Tasks.Task.FromResult(new List<CoreLocationDto>());
            var parentTasksTask = parentTaskIds.Any() ? _taskRepository.GetTasksByIdsAsync(parentTaskIds.ToList()) : System.Threading.Tasks.Task.FromResult(new List<TaskEntity>());
            
            // 批量查询统计信息
            var subTaskCountsTask = _taskRepository.GetSubTaskCountsBatchAsync(taskIds);
            var commentCountsTask = _taskRepository.GetCommentCountsBatchAsync(taskIds);
            var attachmentCountsTask = _taskRepository.GetAttachmentCountsBatchAsync(taskIds);

            // 等待所有查询完成
            try 
            {
                await System.Threading.Tasks.Task.WhenAll(usersTask, assetsTask, locationsTask, parentTasksTask, subTaskCountsTask, commentCountsTask, attachmentCountsTask);
                _logger.LogInformation("所有批量查询完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量查询执行失败");
                throw;
            }

            var users = (await usersTask).ToDictionary(u => u.Id, u => u);
            var assets = (await assetsTask).ToDictionary(a => a.Id, a => a);
            var locations = (await locationsTask).ToDictionary(l => l.Id, l => l);
            var parentTasks = (await parentTasksTask).ToDictionary(t => t.TaskId, t => t);
            var subTaskCounts = await subTaskCountsTask;
            var commentCounts = await commentCountsTask;
            var attachmentCounts = await attachmentCountsTask;

            _logger.LogInformation("数据转换完成 - 用户:{UserCount}, 资产:{AssetCount}, 位置:{LocationCount}, 父任务:{ParentTaskCount}", 
                users.Count, assets.Count, locations.Count, parentTasks.Count);

            // 映射所有任务DTO
            var taskDtos = new List<TaskDto>();
            foreach (var task in tasks)
            {
                var dto = MapTaskToDtoWithCachedData(task, users, assets, locations, parentTasks, 
                    subTaskCounts.GetValueOrDefault(task.TaskId, 0),
                    commentCounts.GetValueOrDefault(task.TaskId, 0),
                    attachmentCounts.GetValueOrDefault(task.TaskId, 0));
                taskDtos.Add(dto);
            }

            return taskDtos;
        }

        /// <summary>
        /// 使用缓存数据映射单个任务DTO
        /// </summary>
        private TaskDto MapTaskToDtoWithCachedData(TaskEntity taskEntity, 
            Dictionary<int, CoreUserDto> users,
            Dictionary<int, CoreAssetDto> assets,
            Dictionary<int, CoreLocationDto> locations,
            Dictionary<long, TaskEntity> parentTasks,
            int subTaskCount,
            int commentCount,
            int attachmentCount)
        {
            var dto = new TaskDto
            {
                TaskId = taskEntity.TaskId,
                Name = taskEntity.Name,
                Description = taskEntity.Description,
                Status = taskEntity.Status,
                StatusName = GetStatusName(taskEntity.Status), // 新增：状态显示名称
                Priority = taskEntity.Priority,
                PriorityText = GetPriorityText(taskEntity.Priority), // 新增：优先级显示名称
                TaskType = taskEntity.TaskType,
                CreationTimestamp = taskEntity.CreationTimestamp,
                ActualEndDate = taskEntity.ActualEndDate,
                PlanEndDate = taskEntity.PlanEndDate,
                PlanStartDate = taskEntity.PlanStartDate,
                ActualStartDate = taskEntity.ActualStartDate,
                Progress = taskEntity.Progress,
                AssigneeUserId = taskEntity.AssigneeUserId,
                CreatorUserId = taskEntity.CreatorUserId,
                AssetId = taskEntity.AssetId,
                LocationId = taskEntity.LocationId,
                ParentTaskId = taskEntity.ParentTaskId,
                PDCAStage = taskEntity.PDCAStage,
                Points = taskEntity.Points,
                LastUpdatedTimestamp = taskEntity.LastUpdatedTimestamp,
                PeriodicTaskScheduleId = taskEntity.PeriodicTaskScheduleId,
                ProjectId = taskEntity.ProjectId,
                IsOverdue = (taskEntity.PlanEndDate.HasValue && taskEntity.PlanEndDate.Value < DateTime.Now &&
                               !("Completed".Equals(taskEntity.Status, StringComparison.OrdinalIgnoreCase) ||
                                 "Done".Equals(taskEntity.Status, StringComparison.OrdinalIgnoreCase) ||
                                 "Canceled".Equals(taskEntity.Status, StringComparison.OrdinalIgnoreCase) ||
                                 "Archived".Equals(taskEntity.Status, StringComparison.OrdinalIgnoreCase))),
                SubTaskCount = subTaskCount,
                CommentCount = commentCount,
                AttachmentCount = attachmentCount
            };

            // 设置负责人信息
            if (taskEntity.AssigneeUserId.HasValue && users.TryGetValue(taskEntity.AssigneeUserId.Value, out var assignee))
            {
                dto.AssigneeUserName = assignee.Username ?? assignee.Name;
                dto.AssigneeAvatarUrl = assignee.AvatarUrl;
            }

            // 设置创建者信息
            if (taskEntity.CreatorUserId > 0 && users.TryGetValue(taskEntity.CreatorUserId, out var creator))
            {
                dto.CreatorUserName = creator.Username ?? creator.Name;
                dto.CreatorUserAvatarUrl = creator.AvatarUrl;
            }

            // 设置父任务名称
            if (taskEntity.ParentTaskId.HasValue && parentTasks.TryGetValue(taskEntity.ParentTaskId.Value, out var parentTask))
            {
                dto.ParentTaskName = parentTask.Name;
            }

            // 设置资产信息
            if (taskEntity.AssetId.HasValue && assets.TryGetValue(taskEntity.AssetId.Value, out var asset))
            {
                dto.AssetName = asset.Name;
                dto.AssetNumber = asset.AssetNumber;
            }

            // 设置位置信息
            if (taskEntity.LocationId.HasValue && locations.TryGetValue(taskEntity.LocationId.Value, out var location))
            {
                dto.LocationName = location.Name;
            }

            // 映射协作者/负责人
            if (taskEntity.Assignees != null && taskEntity.Assignees.Any())
            {
                dto.Assignees = new List<AssigneeDto>();
                dto.Participants = new List<UserBasicDto>();
                
                foreach (var assigneeEntry in taskEntity.Assignees)
                {
                    if (users.TryGetValue(assigneeEntry.UserId, out var user))
                    {
                        var assigneeDto = new AssigneeDto
                        {
                            UserId = user.Id,
                            UserName = user.Name ?? user.Username ?? "未知用户",
                            AvatarUrl = string.IsNullOrEmpty(user.AvatarUrl) ? "" : user.AvatarUrl,
                            AssignmentType = assigneeEntry.AssignmentType ?? "Participant",
                            Role = assigneeEntry.UserId == taskEntity.AssigneeUserId ? "Primary" : "Collaborator",
                            AssignmentTimestamp = assigneeEntry.AssignmentTimestamp
                        };
                        dto.Assignees.Add(assigneeDto);
                        
                        dto.Participants.Add(new UserBasicDto 
                        { 
                            Id = user.Id, 
                            Name = user.Name ?? user.Username ?? "未知用户",
                            AvatarUrl = string.IsNullOrEmpty(user.AvatarUrl) ? "" : user.AvatarUrl
                        });
                    }
                }

                if (dto.Assignees.Count > 0)
                {
                    _logger.LogInformation("任务{TaskId}映射了{Count}个负责人/协作者", taskEntity.TaskId, dto.Assignees.Count);
                }
            }

            return dto;
        }

        #region Periodic Task Schedule Management Implementation

        /// <summary>
        /// 获取周期性任务计划列表（分页）
        /// </summary>
        public async Task<ApiResponse<PaginatedResult<PeriodicTaskScheduleDto>>> GetPeriodicSchedulesPagedAsync(PeriodicTaskScheduleQueryParametersDto parameters)
        {
            try
            {
                // Validate parameters (e.g., PageNumber, PageSize)
                if (parameters.PageNumber <= 0) parameters.PageNumber = 1;
                if (parameters.PageSize <= 0) parameters.PageSize = 10;
                if (parameters.PageSize > 100) parameters.PageSize = 100; // Max page size limit

                var pagedResult = await _taskRepository.GetPeriodicSchedulesPagedAsync(parameters);

                var dtoList = new List<PeriodicTaskScheduleDto>();
                foreach (var schedule in pagedResult.Items)
                {
                    dtoList.Add(await MapPeriodicTaskScheduleToDtoAsync(schedule));
                }

                var paginatedDtoResult = PaginatedResult<PeriodicTaskScheduleDto>.Create(dtoList, pagedResult.TotalCount, parameters.PageNumber, parameters.PageSize);
                return ApiResponse<PaginatedResult<PeriodicTaskScheduleDto>>.CreateSuccess(paginatedDtoResult, "获取周期性任务计划成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "分页获取周期性任务计划时发生错误。");
                return ApiResponse<PaginatedResult<PeriodicTaskScheduleDto>>.CreateFail("分页获取周期性任务计划时发生错误: " + ex.Message);
            }
        }

        /// <summary>
        /// 根据ID获取周期性任务计划详情
        /// </summary>
        public async Task<ApiResponse<PeriodicTaskScheduleDto>> GetPeriodicScheduleByIdAsync(long scheduleId)
        {
            try
            {
                var schedule = await _taskRepository.GetPeriodicScheduleByIdAsync(scheduleId);
                if (schedule == null)
                {
                    return ApiResponse<PeriodicTaskScheduleDto>.CreateFail("未找到指定的周期性任务计划。"); // Removed StatusCodes
                }
                var dto = await MapPeriodicTaskScheduleToDtoAsync(schedule);
                return ApiResponse<PeriodicTaskScheduleDto>.CreateSuccess(dto, "获取周期性任务计划详情成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取周期性任务计划 (ID={ScheduleId}) 详情时发生错误。", scheduleId);
                return ApiResponse<PeriodicTaskScheduleDto>.CreateFail("获取周期性任务计划详情时发生错误: " + ex.Message);
            }
        }

        /// <summary>
        /// 创建新的周期性任务计划
        /// </summary>
        public async Task<ApiResponse<PeriodicTaskScheduleDto>> CreatePeriodicScheduleAsync(CreatePeriodicTaskScheduleRequestDto request, int currentUserId)
        {
            try
            {
                // 处理模板任务：如果TemplateTaskId为0或不存在，则创建一个新的模板任务
                TaskEntity? templateTask = null;
                if (request.TemplateTaskId > 0)
                {
                    templateTask = await _taskRepository.GetTaskByIdAsync(request.TemplateTaskId);
                }

                if (templateTask == null)
                {
                    // 创建新的模板任务
                    templateTask = new TaskEntity
                    {
                        Name = request.TaskTemplateTitle ?? request.Name + " - 模板",
                        Description = request.TaskTemplateDescription ?? "由周期性任务计划自动生成的模板任务",
                        Status = "Template", // 标记为模板任务
                        Priority = request.DefaultPriority?.ToString() ?? "Medium",
                        TaskType = "Template",
                        CreatorUserId = currentUserId,
                        AssigneeUserId = request.DefaultAssigneeUserId,
                        CreationTimestamp = DateTime.Now,
                        LastUpdatedTimestamp = DateTime.Now,
                        PlanStartDate = DateTime.Today,
                        PlanEndDate = DateTime.Today.AddDays(request.DefaultDurationDays ?? 1),
                        Progress = 0,
                        Points = 0
                    };

                    templateTask = await _taskRepository.AddTaskAsync(templateTask);
                    _logger.LogInformation("为周期性任务计划创建了新的模板任务 ID: {TemplateTaskId}", templateTask.TaskId);
                }

                // Validate Cron Expression if provided (simple check for now)
                if (string.IsNullOrWhiteSpace(request.CronExpression))
                {
                     return ApiResponse<PeriodicTaskScheduleDto>.CreateFail("Cron表达式不能为空。");
                }
                try 
                {
                    Cronos.CronExpression.Parse(request.CronExpression, Cronos.CronFormat.Standard);
                }
                catch (Cronos.CronFormatException cronEx)
                {
                    return ApiResponse<PeriodicTaskScheduleDto>.CreateFail($"无效的Cron表达式: {cronEx.Message}");
                }

                // 描述不再包含多负责人信息，多负责人信息将存储在单独的表中
                var scheduleDescription = request.Description ?? "";

                var schedule = new ItAssetsSystem.Domain.Entities.Tasks.PeriodicTaskSchedule
                {
                    Name = request.Name,
                    Description = scheduleDescription,
                    TemplateTaskId = templateTask.TaskId, // 使用模板任务的ID
                    CreatorUserId = currentUserId,
                    CronExpression = request.CronExpression,
                    StartDate = request.StartDate,
                    EndDate = request.EndDate,
                    Status = "Active", // Default status
                    NextGenerationTime = CalculateNextGenerationTime(request.StartDate, request.CronExpression), // Calculate initial next gen time
                    CreationTimestamp = DateTime.Now,
                    LastUpdatedTimestamp = DateTime.Now,
                    // Other properties from request as needed by the V2 entity
                    RecurrenceType = request.RecurrenceType ?? "CustomCron", // Default if not provided, assuming CronExpression is primary
                    RecurrenceInterval = request.RecurrenceInterval ?? 1,
                    // 使用正确的类型 - 实体DayOfMonth是int?，而WeekOfMonth和DayOfWeekForMonth是string?
                    DaysOfWeek = request.DaysOfWeek,
                    DayOfMonth = request.DayOfMonth, // 直接使用，都是int?
                    WeekOfMonth = request.WeekOfMonth?.ToString(), // int? 转 string?
                    DayOfWeekForMonth = request.DayOfWeekForMonth?.ToString(), // int? 转 string?
                    MonthOfYear = request.MonthOfYear, // 直接使用，都是int?
                    EndConditionType = request.EndConditionType ?? "Never", // 设置默认值
                    TotalOccurrences = request.TotalOccurrences
                    // DefaultPoints, etc., might come from templateTask or request.
                };

                var createdSchedule = await _taskRepository.AddPeriodicScheduleAsync(schedule);

                // 添加负责人关联
                if (request.DefaultAssigneeUserIds != null && request.DefaultAssigneeUserIds.Count > 0)
                {
                    foreach (var userId in request.DefaultAssigneeUserIds)
                    {
                        var assignee = new ItAssetsSystem.Domain.Entities.Tasks.PeriodicTaskScheduleAssignee
                        {
                            PeriodicTaskScheduleId = createdSchedule.PeriodicTaskScheduleId,
                            UserId = userId,
                            CreatedAt = DateTime.Now
                        };
                        await _taskRepository.AddPeriodicScheduleAssigneeAsync(assignee);
                    }
                }

                var dto = await MapPeriodicTaskScheduleToDtoAsync(createdSchedule);

                return ApiResponse<PeriodicTaskScheduleDto>.CreateSuccess(dto, "周期性任务计划创建成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建周期性任务计划时发生错误。");
                return ApiResponse<PeriodicTaskScheduleDto>.CreateFail("创建周期性任务计划时发生错误: " + ex.Message);
            }
        }

        /// <summary>
        /// 更新现有的周期性任务计划
        /// </summary>
        public async Task<ApiResponse<PeriodicTaskScheduleDto>> UpdatePeriodicScheduleAsync(long scheduleId, UpdatePeriodicTaskScheduleRequestDto request, int currentUserId)
        {
            try
            {
                var schedule = await _taskRepository.GetPeriodicScheduleByIdAsync(scheduleId);
                if (schedule == null)
                {
                    return ApiResponse<PeriodicTaskScheduleDto>.CreateFail("未找到要更新的周期性任务计划。");
                }

                // 处理模板任务更新
                if (request.TemplateTaskId.HasValue)
                {
                    TaskEntity? templateTask = null;
                    if (request.TemplateTaskId.Value > 0)
                    {
                        templateTask = await _taskRepository.GetTaskByIdAsync(request.TemplateTaskId.Value);
                    }

                    if (templateTask == null)
                    {
                        // 如果模板任务不存在或ID为0，更新现有的模板任务
                        var existingTemplateTask = await _taskRepository.GetTaskByIdAsync(schedule.TemplateTaskId);
                        if (existingTemplateTask != null)
                        {
                            // 更新现有模板任务的信息
                            if (!string.IsNullOrEmpty(request.TaskTemplateTitle))
                            {
                                existingTemplateTask.Name = request.TaskTemplateTitle;
                            }
                            if (!string.IsNullOrEmpty(request.TaskTemplateDescription))
                            {
                                existingTemplateTask.Description = request.TaskTemplateDescription;
                            }
                            if (request.DefaultPriority.HasValue)
                            {
                                existingTemplateTask.Priority = request.DefaultPriority.Value.ToString();
                            }
                            if (request.DefaultAssigneeUserId.HasValue)
                            {
                                existingTemplateTask.AssigneeUserId = request.DefaultAssigneeUserId.Value;
                            }
                            existingTemplateTask.LastUpdatedTimestamp = DateTime.Now;

                            await _taskRepository.UpdateTaskAsync(existingTemplateTask);
                            _logger.LogInformation("更新了周期性任务计划的模板任务 ID: {TemplateTaskId}", existingTemplateTask.TaskId);
                        }
                    }
                    else
                    {
                        // 使用指定的模板任务
                        schedule.TemplateTaskId = templateTask.TaskId;
                    }
                }

                // Validate Cron Expression if changed
                if (!string.IsNullOrWhiteSpace(request.CronExpression) && request.CronExpression != schedule.CronExpression)
                {
                    try 
                    {
                         Cronos.CronExpression.Parse(request.CronExpression, Cronos.CronFormat.Standard);
                         schedule.CronExpression = request.CronExpression;
                         // Recalculate NextGenerationTime if cron or start date changes that affect it
                         schedule.NextGenerationTime = CalculateNextGenerationTime(schedule.StartDate, schedule.CronExpression);
                    }
                    catch (Cronos.CronFormatException cronEx)
                    {
                        return ApiResponse<PeriodicTaskScheduleDto>.CreateFail($"无效的Cron表达式: {cronEx.Message}");
                    }
                }

                schedule.Name = request.Name ?? schedule.Name;
                schedule.Description = request.Description ?? schedule.Description;

                // 更新负责人关联
                if (request.DefaultAssigneeUserIds != null)
                {
                    // 删除现有的负责人关联
                    await _taskRepository.DeletePeriodicScheduleAssigneesAsync(scheduleId);

                    // 添加新的负责人关联
                    if (request.DefaultAssigneeUserIds.Count > 0)
                    {
                        foreach (var userId in request.DefaultAssigneeUserIds)
                        {
                            var assignee = new ItAssetsSystem.Domain.Entities.Tasks.PeriodicTaskScheduleAssignee
                            {
                                PeriodicTaskScheduleId = scheduleId,
                                UserId = userId,
                                CreatedAt = DateTime.Now
                            };
                            await _taskRepository.AddPeriodicScheduleAssigneeAsync(assignee);
                        }
                    }
                }
                schedule.StartDate = request.StartDate ?? schedule.StartDate;
                schedule.EndDate = request.EndDate; // Can be set to null
                schedule.Status = request.Status ?? schedule.Status;
                schedule.LastUpdatedTimestamp = DateTime.Now;
                // Update other properties as needed
                schedule.RecurrenceType = request.RecurrenceType ?? schedule.RecurrenceType;
                schedule.RecurrenceInterval = request.RecurrenceInterval ?? schedule.RecurrenceInterval;
                schedule.DaysOfWeek = request.DaysOfWeek ?? schedule.DaysOfWeek;
                // 使用正确的类型转换
                schedule.DayOfMonth = request.DayOfMonth ?? schedule.DayOfMonth; // 都是int?
                schedule.WeekOfMonth = request.WeekOfMonth.HasValue ? request.WeekOfMonth.Value.ToString() : schedule.WeekOfMonth; // int? -> string?
                schedule.DayOfWeekForMonth = request.DayOfWeekForMonth.HasValue ? request.DayOfWeekForMonth.Value.ToString() : schedule.DayOfWeekForMonth; // int? -> string?
                schedule.MonthOfYear = request.MonthOfYear ?? schedule.MonthOfYear; // 都是int?

                // If StartDate or CronExpression changed, recalculate NextGenerationTime
                if ((request.StartDate.HasValue && request.StartDate.Value != schedule.StartDate) || 
                    (!string.IsNullOrWhiteSpace(request.CronExpression) && request.CronExpression != schedule.CronExpression))
                {
                    schedule.NextGenerationTime = CalculateNextGenerationTime(schedule.StartDate, schedule.CronExpression ?? "* * * * *");
                }


                var success = await _taskRepository.UpdatePeriodicScheduleAsync(schedule);
                if (!success)
                {
                    return ApiResponse<PeriodicTaskScheduleDto>.CreateFail("更新周期性任务计划失败。数据库操作未成功。");
                }

                var updatedDto = await MapPeriodicTaskScheduleToDtoAsync(schedule);
                return ApiResponse<PeriodicTaskScheduleDto>.CreateSuccess(updatedDto, "周期性任务计划更新成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新周期性任务计划 (ID={ScheduleId}) 时发生错误。", scheduleId);
                return ApiResponse<PeriodicTaskScheduleDto>.CreateFail("更新周期性任务计划时发生错误: " + ex.Message);
            }
        }

        /// <summary>
        /// 删除周期性任务计划（软删除）
        /// </summary>
        public async Task<ApiResponse<bool>> DeletePeriodicScheduleAsync(long scheduleId, int currentUserId)
        {
            try
            {
                // CurrentUserId might be used for logging or authorization checks if needed.
                var success = await _taskRepository.DeletePeriodicScheduleAsync(scheduleId); // Repository handles soft delete
                if (!success)
                {
                    return ApiResponse<bool>.CreateFail("未找到要删除的周期性任务计划或删除失败。"); // Removed StatusCodes
                }
                return ApiResponse<bool>.CreateSuccess(true, "周期性任务计划删除成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除周期性任务计划 (ID={ScheduleId}) 时发生错误。", scheduleId);
                return ApiResponse<bool>.CreateFail("删除周期性任务计划时发生错误: " + ex.Message);
            }
        }

        /// <summary>
        /// 启用或禁用周期性任务计划
        /// </summary>
        public async Task<ApiResponse<bool>> EnablePeriodicScheduleAsync(long scheduleId, bool isEnabled, int currentUserId)
        {
            try
            {
                var success = await _taskRepository.EnablePeriodicScheduleAsync(scheduleId, isEnabled);
                if (!success)
                {
                    return ApiResponse<bool>.CreateFail("未找到要操作的周期性任务计划或操作失败。"); // Removed StatusCodes
                }
                return ApiResponse<bool>.CreateSuccess(true, isEnabled ? "周期性任务计划已启用" : "周期性任务计划已禁用");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "启用/禁用周期性任务计划 (ID={ScheduleId}, IsEnabled={IsEnabled}) 时发生错误。", scheduleId, isEnabled);
                return ApiResponse<bool>.CreateFail("启用/禁用周期性任务计划时发生错误: " + ex.Message);
            }
        }

        // Helper method to map Entity to DTO
        private async Task<PeriodicTaskScheduleDto> MapPeriodicTaskScheduleToDtoAsync(ItAssetsSystem.Domain.Entities.Tasks.PeriodicTaskSchedule schedule)
        {
            var creatorName = "未知用户";
            if (schedule.CreatorUserId > 0)
            {
                var creatorUser = await _coreDataQueryService.GetUserAsync(schedule.CreatorUserId);
                if (creatorUser != null)
                {
                    creatorName = !string.IsNullOrWhiteSpace(creatorUser.Name) ? creatorUser.Name : creatorUser.Username;
                }
            }

            string? templateTaskName = null;
            string? templateTaskDescription = null;
            int? templateDefaultAssigneeId = null;
            TaskPriority templateDefaultPriority = TaskPriority.Medium;
            int templateDefaultDurationDays = 1;
            int? templateDefaultAssetId = null;
            int? templateDefaultLocationId = null;
            int templateDefaultPoints = 0;

            if (schedule.TemplateTaskId > 0)
            {
                var templateTask = await _taskRepository.GetTaskByIdAsync(schedule.TemplateTaskId);
                if (templateTask != null)
                {
                    templateTaskName = templateTask.Name ?? string.Empty; // Provide default if null
                    templateTaskDescription = templateTask.Description ?? string.Empty; // Provide default if null
                    templateDefaultAssigneeId = templateTask.AssigneeUserId;
                    System.Enum.TryParse(templateTask.Priority, true, out templateDefaultPriority);                    
                    // templateDefaultDurationDays could be derived from PlanEndDate - PlanStartDate if needed
                    templateDefaultAssetId = templateTask.AssetId;
                    templateDefaultLocationId = templateTask.LocationId;
                    templateDefaultPoints = templateTask.Points;
                }
            }
            
            // 从新表中获取多负责人信息
            var assigneeUserIds = new List<int>();
            string? finalDefaultAssigneeName = null;

            var assignees = await _taskRepository.GetPeriodicScheduleAssigneesAsync(schedule.PeriodicTaskScheduleId);
            if (assignees != null && assignees.Count > 0)
            {
                assigneeUserIds.AddRange(assignees.Select(a => a.UserId));
                _logger.LogInformation("从数据库获取到 {Count} 个负责人: {Ids}", assignees.Count, string.Join(",", assigneeUserIds));
            }

            // 如果没有从描述中解析到负责人，使用模板任务的负责人
            if (assigneeUserIds.Count == 0 && templateDefaultAssigneeId.HasValue && templateDefaultAssigneeId.Value > 0)
            {
                assigneeUserIds.Add(templateDefaultAssigneeId.Value);
            }

            // 获取第一个负责人的名称作为显示名称
            if (assigneeUserIds.Count > 0)
            {
                var assigneeUser = await _coreDataQueryService.GetUserAsync(assigneeUserIds[0]);
                if (assigneeUser != null)
                {
                    finalDefaultAssigneeName = !string.IsNullOrWhiteSpace(assigneeUser.Name) ? assigneeUser.Name : assigneeUser.Username;
                }
            }

            string? recurrenceDesc = null;
            if (!string.IsNullOrEmpty(schedule.CronExpression))
            {
                try
                {
                    // 配置中文和24小时制格式
                    var options = new Options()
                    {
                        Locale = "zh-CN",
                        Use24HourTimeFormat = true,
                        ThrowExceptionOnParseError = false
                    };

                    recurrenceDesc = ExpressionDescriptor.GetDescription(schedule.CronExpression, options);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning("无法获取Cron表达式 {CronExpression} 的描述: {ErrorMessage}", schedule.CronExpression, ex.Message);
                    recurrenceDesc = "自定义Cron表达式";
                }
            }

            return new PeriodicTaskScheduleDto
            {
                Id = schedule.PeriodicTaskScheduleId,
                Name = schedule.Name,
                Description = schedule.Description,
                
                TemplateTaskId = schedule.TemplateTaskId,
                TemplateTaskName = templateTaskName,
                TaskTemplateTitle = templateTaskName,
                TaskTemplateDescription = templateTaskDescription,

                RecurrenceType = schedule.RecurrenceType,
                RecurrenceInterval = schedule.RecurrenceInterval,
                DaysOfWeek = schedule.DaysOfWeek,
                // 根据实体属性设置DTO属性
                DayOfMonth = schedule.DayOfMonth, // 直接使用，都是int?
                // 尝试转换string?到int?
                WeekOfMonth = !string.IsNullOrEmpty(schedule.WeekOfMonth) && int.TryParse(schedule.WeekOfMonth, out int weekOfMonth) ? (int?)weekOfMonth : null,
                DayOfWeekForMonth = !string.IsNullOrEmpty(schedule.DayOfWeekForMonth) && int.TryParse(schedule.DayOfWeekForMonth, out int dayOfWeekForMonth) ? (int?)dayOfWeekForMonth : null,
                MonthOfYear = schedule.MonthOfYear, // 直接使用，都是int?
                CronExpression = schedule.CronExpression,
                RecurrenceRule = schedule.CronExpression ?? "", // 设置默认值避免null
                RecurrenceDescription = recurrenceDesc ?? "自定义Cron表达式",

                StartDate = schedule.StartDate,
                EndDate = schedule.EndDate,
                EndConditionType = schedule.EndConditionType,
                TotalOccurrences = schedule.TotalOccurrences,
                OccurrencesGenerated = schedule.OccurrencesGenerated,

                NextGenerationTime = schedule.NextGenerationTime,
                LastGenerationTime = schedule.LastGeneratedTimestamp,
                CreatedAt = schedule.CreationTimestamp, // 映射CreatedAt
                UpdatedAt = schedule.LastUpdatedTimestamp, // 映射UpdatedAt
                
                DefaultAssigneeUserId = assigneeUserIds.Count > 0 ? assigneeUserIds[0] : (templateDefaultAssigneeId ?? 0),
                DefaultAssigneeUserName = finalDefaultAssigneeName,
                DefaultAssigneeUserIds = assigneeUserIds.Count > 0 ? assigneeUserIds : null,

                CreatorUserId = schedule.CreatorUserId,
                CreatorUserName = creatorName,
                
                CreationTimestamp = schedule.CreationTimestamp,
                LastUpdatedTimestamp = schedule.LastUpdatedTimestamp,
                
                Status = schedule.Status,
                IsEnabled = "Active".Equals(schedule.Status, StringComparison.OrdinalIgnoreCase),

                DefaultPoints = templateDefaultPoints,
                DefaultPriority = templateDefaultPriority.ToString(),
                DefaultPriorityDisplayName = templateDefaultPriority.ToString(), // 设置显示名称
                DefaultDurationDays = templateDefaultDurationDays,
                DefaultAssetId = templateDefaultAssetId,
                DefaultLocationId = templateDefaultLocationId,

                RecentGeneratedTasks = new List<TaskDto>()
            };
        }

        // Helper method to calculate next generation time
        private DateTime CalculateNextGenerationTime(DateTime startTime, string cronExpression)
        {
            var expression = Cronos.CronExpression.Parse(cronExpression, Cronos.CronFormat.Standard);

            // 确保startTime是本地时间
            var startTimeLocal = startTime.Kind == DateTimeKind.Utc ? startTime.ToLocalTime() : startTime;
            var nowLocal = DateTime.Now;

            // 使用UTC时间进行计算，但指定本地时区
            var nowUtc = DateTime.UtcNow;
            var nextOccurrenceUtc = expression.GetNextOccurrence(nowUtc, TimeZoneInfo.Local);

            // 如果计算出的下次执行时间早于开始时间，则从开始时间计算
            if (nextOccurrenceUtc.HasValue)
            {
                var nextOccurrenceLocal = nextOccurrenceUtc.Value.ToLocalTime();
                if (nextOccurrenceLocal < startTimeLocal)
                {
                    // 从开始时间重新计算
                    var startTimeUtc = startTimeLocal.ToUniversalTime();
                    nextOccurrenceUtc = expression.GetNextOccurrence(startTimeUtc.AddTicks(-1), TimeZoneInfo.Local);
                    nextOccurrenceLocal = nextOccurrenceUtc?.ToLocalTime() ?? startTimeLocal.AddYears(100);
                }
                return nextOccurrenceLocal;
            }

            return startTimeLocal.AddYears(100);
        }

        #endregion

        #region 状态和优先级映射方法

        /// <summary>
        /// 获取状态显示名称
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>状态显示名称</returns>
        private string GetStatusName(string status)
        {
            return status?.ToLower() switch
            {
                "todo" => "待处理",
                "inprogress" => "进行中",
                "review" => "待审核",
                "done" => "已完成",
                "completed" => "已完成",
                "cancelled" => "已取消",
                "canceled" => "已取消",
                "archived" => "已归档",
                "pending" => "待开始",
                _ => status ?? "未知"
            };
        }

        /// <summary>
        /// 获取优先级显示名称
        /// </summary>
        /// <param name="priority">优先级值</param>
        /// <returns>优先级显示名称</returns>
        private string GetPriorityText(string priority)
        {
            return priority?.ToLower() switch
            {
                "low" => "低",
                "medium" => "中",
                "high" => "高",
                "urgent" => "紧急",
                _ => priority ?? "未知"
            };
        }

        #endregion
    }
}