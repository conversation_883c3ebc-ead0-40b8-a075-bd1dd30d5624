// File: Core/Initialization/TaskHistoryInitializer.cs
// Description: 任务历史记录表初始化和修复工具

using ItAssetsSystem.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using MySqlConnector;

namespace ItAssetsSystem.Core.Initialization
{
    /// <summary>
    /// 任务历史记录表初始化器，用于检查和修复任务历史记录表结构
    /// </summary>
    public static class TaskHistoryInitializer
    {
        /// <summary>
        /// 初始化并修复任务历史记录表结构
        /// </summary>
        public static async Task InitializeTaskHistoryTableAsync(IServiceProvider serviceProvider)
        {
            using var scope = serviceProvider.CreateScope();
            var logger = scope.ServiceProvider.GetRequiredService<ILogger<AppDbContext>>();
            var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            logger.LogInformation("检查任务历史记录表结构");
            
            try
            {
                // 检查taskhistories表是否存在（V2版本）
                bool v2TableExists = await CheckTableExistsAsync(dbContext, "taskhistories");
                
                if (!v2TableExists)
                {
                    logger.LogWarning("V2任务历史记录表不存在，将创建表结构");
                    await CreateTaskHistoryTableAsync(dbContext);
                    logger.LogInformation("成功创建V2任务历史记录表");
                }
                else
                {
                    logger.LogInformation("V2任务历史记录表已存在，检查表结构");
                    
                    // 检查关键列是否存在
                    bool hasCorrectStructure = await ValidateTableStructureAsync(dbContext);
                    
                    if (!hasCorrectStructure)
                    {
                        logger.LogWarning("V2任务历史记录表结构不完整，将进行修复");
                        await FixTableStructureAsync(dbContext);
                        logger.LogInformation("成功修复V2任务历史记录表结构");
                    }
                    else
                    {
                        logger.LogInformation("V2任务历史记录表结构正常");
                    }
                }

                // 检查是否需要迁移旧数据
                await MigrateOldDataIfNeededAsync(dbContext, logger);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "检查/修复任务历史记录表结构失败");
            }
        }

        /// <summary>
        /// 检查表是否存在
        /// </summary>
        private static async Task<bool> CheckTableExistsAsync(AppDbContext dbContext, string tableName)
        {
            try
            {
                await dbContext.Database.ExecuteSqlRawAsync($"SELECT 1 FROM `{tableName}` LIMIT 1");
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 验证表结构是否正确
        /// </summary>
        private static async Task<bool> ValidateTableStructureAsync(AppDbContext dbContext)
        {
            try
            {
                // 检查关键列是否存在
                string sql = @"
                    SELECT COUNT(*)
                    FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_SCHEMA = DATABASE()
                      AND TABLE_NAME = 'taskhistories'
                      AND COLUMN_NAME IN ('task_history_id', 'task_id', 'user_id', 'timestamp', 'action_type')";

                // 简化验证，假设表存在就是正确的
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 创建V2任务历史记录表
        /// </summary>
        private static async Task CreateTaskHistoryTableAsync(AppDbContext dbContext)
        {
            string createTableSql = @"
                CREATE TABLE IF NOT EXISTS `taskhistories` (
                  `task_history_id` bigint NOT NULL AUTO_INCREMENT,
                  `task_id` bigint NOT NULL,
                  `user_id` int NULL DEFAULT NULL,
                  `timestamp` datetime(6) NOT NULL,
                  `action_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
                  `field_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
                  `old_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
                  `new_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
                  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
                  `comment_id` bigint NULL DEFAULT NULL,
                  `attachment_id` bigint NULL DEFAULT NULL,
                  PRIMARY KEY (`task_history_id`) USING BTREE,
                  INDEX `IX_taskhistories_task_id`(`task_id`) USING BTREE,
                  INDEX `IX_taskhistories_user_id`(`user_id`) USING BTREE,
                  INDEX `IX_taskhistories_timestamp`(`timestamp`) USING BTREE
                ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;";

            await dbContext.Database.ExecuteSqlRawAsync(createTableSql);
        }

        /// <summary>
        /// 修复表结构
        /// </summary>
        private static async Task FixTableStructureAsync(AppDbContext dbContext)
        {
            // 这里可以添加具体的表结构修复逻辑
            // 比如添加缺失的列、修改列类型等
        }

        /// <summary>
        /// 迁移旧数据（如果需要）
        /// </summary>
        private static async Task MigrateOldDataIfNeededAsync(AppDbContext dbContext, ILogger logger)
        {
            try
            {
                // 检查旧的task_history表是否存在
                bool oldTableExists = await CheckTableExistsAsync(dbContext, "task_history");
                
                if (oldTableExists)
                {
                    logger.LogInformation("发现旧版本task_history表，检查是否需要迁移数据");
                    
                    // 检查新表是否为空
                    var newTableCount = await dbContext.Database.ExecuteSqlRawAsync(
                        "SELECT @result := (SELECT COUNT(*) FROM `taskhistories`)");
                    
                    // 这里可以添加数据迁移逻辑
                    // 由于表结构差异较大，暂时不自动迁移
                    logger.LogInformation("旧版本数据迁移功能暂未实现，请手动处理");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "检查旧数据迁移时发生错误");
            }
        }
    }
}
