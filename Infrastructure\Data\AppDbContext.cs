// File: Infrastructure/Data/AppDbContext.cs
// Description: 完全重构的 Entity Framework DbContext，区分核心(INT PK)与V2模块(BIGINT PK)。

#nullable enable
using System;
using System.Reflection;
using Microsoft.EntityFrameworkCore;
using ItAssetsSystem.Models.Entities; // 核心实体 (INT PKs) - User, Asset, Location, etc.
using ItAssetsSystem.Domain.Entities.Tasks; // V2 任务模块实体 (BIGINT PKs)
using ItAssetsSystem.Domain.Entities.Notes; // V2 笔记模块实体 (BIGINT PKs)
// using ItAssetsSystem.Domain.Entities.Gamification; // V2 游戏化模块实体 (BIGINT PKs) - Commented out
// using ItAssetsSystem.Domain.Entities.Notifications; // V2 通知模块实体 (BIGINT PKs) - Commented out
using ItAssetsSystem.Infrastructure.Data.Extensions; // For snake case convention
using EFCore.NamingConventions; // For UseSnakeCaseNamingConvention
using ItAssetsSystem.Models; // For IAuditableEntity (假设在这里)
using System.Threading; // Added for CancellationToken
using ItAssetsSystem.Domain.Entities;

// 显式 using 别名以尝试解决二义性
using CoreLocationUser = ItAssetsSystem.Models.Entities.LocationUser;
using CoreLocation = ItAssetsSystem.Models.Entities.Location;
using CorePersonnel = ItAssetsSystem.Models.Entities.Personnel;

// 需要显式引用 Domain 实体，避免与 Models.Entities 冲突
using Task = ItAssetsSystem.Domain.Entities.Tasks.Task;
using Comment = ItAssetsSystem.Domain.Entities.Tasks.Comment;
using Attachment = ItAssetsSystem.Domain.Entities.Tasks.Attachment;
using TaskHistory = ItAssetsSystem.Domain.Entities.Tasks.TaskHistory;
using TaskAssignee = ItAssetsSystem.Domain.Entities.Tasks.TaskAssignee;
using PeriodicTaskSchedule = ItAssetsSystem.Domain.Entities.Tasks.PeriodicTaskSchedule;
using PdcaPlan = ItAssetsSystem.Domain.Entities.Tasks.PdcaPlan;
using QuickMemo = ItAssetsSystem.Domain.Entities.Notes.QuickMemo;
using QuickMemoCategory = ItAssetsSystem.Domain.Entities.Notes.QuickMemoCategory;

// -- Gamification & Notification using directives --
// using GamificationBadge = ItAssetsSystem.Domain.Entities.Gamification.GamificationBadge; // Commented out
// ... etc ...
using Notification = ItAssetsSystem.Domain.Entities.Notification;


namespace ItAssetsSystem.Infrastructure.Data
{
    public class AppDbContext : DbContext
    {
        public AppDbContext(DbContextOptions<AppDbContext> options)
            : base(options)
        {
        }

        // --- 核心模块 DbSets (INT PKs, from Models.Entities) ---
        public DbSet<User> Users { get; set; } = null!;
        public DbSet<Role> Roles { get; set; } = null!;
        public DbSet<Permission> Permissions { get; set; } = null!;
        public DbSet<UserRole> UserRoles { get; set; } = null!;
        public DbSet<RolePermission> RolePermissions { get; set; } = null!;
        public DbSet<Menu> Menus { get; set; } = null!;
        public DbSet<RoleMenu> RoleMenus { get; set; } = null!;
        public DbSet<Department> Departments { get; set; } = null!;
        public DbSet<Location> Locations { get; set; } = null!;
        public DbSet<Personnel> Personnel { get; set; } = null!;
        public DbSet<Asset> Assets { get; set; } = null!;
        public DbSet<AssetType> AssetTypes { get; set; } = null!;
        // ... 其他核心实体 DbSets (FaultRecord, PurchaseOrder, etc.) ...
        public DbSet<LocationHistory> LocationHistories { get; set; } = null!;
        public DbSet<AssetHistory> AssetHistories { get; set; } = null!;
        public DbSet<FaultRecord> FaultRecords { get; set; } = null!;
        public DbSet<FaultType> FaultTypes { get; set; } = null!;
        public DbSet<Supplier> Suppliers { get; set; } = null!;
        public DbSet<PurchaseOrder> PurchaseOrders { get; set; } = null!;
        public DbSet<PurchaseItem> PurchaseItems { get; set; } = null!;
        public DbSet<ReturnToFactory> ReturnToFactories { get; set; } = null!;
        public DbSet<AssetReceive> AssetReceives { get; set; } = null!;
        public DbSet<MaintenanceOrder> MaintenanceOrders { get; set; } = null!;
        public DbSet<RefreshToken> RefreshTokens { get; set; } = null!;
        public DbSet<AuditLog> AuditLogs { get; set; } = null!;
        public DbSet<LocationUser> LocationUsers { get; set; } = null!;

        // --- V2 任务模块 DbSets (BIGINT PKs, from Domain.Entities.Tasks) ---
        public DbSet<Task> Tasks { get; set; } = null!;
        public DbSet<Comment> Comments { get; set; } = null!;
        public DbSet<Attachment> Attachments { get; set; } = null!;
        public DbSet<TaskHistory> TaskHistories { get; set; } = null!;
        public DbSet<TaskAssignee> TaskAssignees { get; set; } = null!;
        public DbSet<PeriodicTaskSchedule> PeriodicTaskSchedules { get; set; } = null!;
        public DbSet<PeriodicTaskScheduleAssignee> PeriodicTaskScheduleAssignees { get; set; } = null!;
        public DbSet<PdcaPlan> PdcaPlans { get; set; } = null!;
        public DbSet<QuickMemo> QuickMemos { get; set; } = null!;
        public DbSet<QuickMemoCategory> QuickMemoCategories { get; set; } = null!;

        // --- V2 游戏化模块 DbSets (BIGINT PKs, from Domain.Entities.Gamification) - Commented out ---
        // public DbSet<GamificationBadge> GamificationBadges { get; set; } = null!;
        // public DbSet<GamificationItem> GamificationItems { get; set; } = null!;
        // public DbSet<GamificationLog> GamificationLogs { get; set; } = null!;
        // public DbSet<GamificationUserBadge> GamificationUserBadges { get; set; } = null!;
        // public DbSet<GamificationUserItem> GamificationUserItems { get; set; } = null!;
        // public DbSet<GamificationUserStat> GamificationUserStats { get; set; } = null!;
        // public DbSet<PointLeaderboard> PointLeaderboards { get; set; } = null!;

        // --- V2 通知模块 DbSets (BIGINT PKs, from Domain.Entities.Notifications) ---
        public DbSet<Notification> Notifications { get; set; } = null!;

        // V2 笔记模块的 DbSet QuickNotes 已在第 79 行正确定义:
        // public DbSet<ItAssetsSystem.Domain.Entities.Notes.QuickNote> QuickNotes { get; set; } = null!;
        // 因此，下面这行 NotesQuickNotes 是多余且错误的，已被移除。
        // public DbSet<Notes.QuickNote> NotesQuickNotes { get; set; } = null!; 

        // 备品备件管理模块
        public DbSet<SparePart> SpareParts { get; set; } = null!;
        public DbSet<SparePartType> SparePartTypes { get; set; } = null!;
        public DbSet<SparePartLocation> SparePartLocations { get; set; } = null!;
        public DbSet<SparePartTransaction> SparePartTransactions { get; set; } = null!;

        // --- V2 返修模块 DbSets (INT PKs, from Domain.Entities) ---
        public DbSet<RepairOrder> RepairOrders { get; set; } = null!;
        public DbSet<RepairItem> RepairItems { get; set; } = null!;
        public DbSet<AssetSnapshot> AssetSnapshots { get; set; } = null!;

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
            
            // 此方法会自动加载当前程序集中所有实现了 IEntityTypeConfiguration 的配置类
            // 这是管理配置的最佳实践，避免 OnModelCreating 方法无限膨胀
            modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());

            // 保持原有的核心配置方法，以确保向后兼容
            ConfigureCoreModuleFallback(modelBuilder);
        }

        // =================== 配置方法 ===================

        private void ConfigureCoreModuleFallback(ModelBuilder modelBuilder)
        {
            // --- User (INT PK) ---
            modelBuilder.Entity<User>(entity =>
            {
                entity.ToTable("users"); // 显式指定表名 (虽然可能被约定覆盖)
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).HasColumnName("id").ValueGeneratedOnAdd(); // 确认INT自增
                // ... User 的其他属性映射和关系 (与 Role, Department 等核心实体的关系) ...
                entity.HasIndex(u => u.Username).IsUnique();
                entity.HasIndex(u => u.Email).IsUnique();
            });

            // --- Role (INT PK) ---
            modelBuilder.Entity<Role>(entity =>
            {
                entity.ToTable("roles");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).HasColumnName("id").ValueGeneratedOnAdd();
                // ... Role 的其他配置 ...
            });

             // --- UserRole (Composite INT PKs) ---
            modelBuilder.Entity<UserRole>(entity =>
            {
                entity.ToTable("userroles");
                entity.HasKey(ur => new { ur.UserId, ur.RoleId });
                entity.HasOne(ur => ur.User).WithMany(u => u.UserRoles).HasForeignKey(ur => ur.UserId);
                entity.HasOne(ur => ur.Role).WithMany(r => r.UserRoles).HasForeignKey(ur => ur.RoleId);
            });

            // --- RoleMenu (Composite INT PKs for Many-to-Many) ---
            modelBuilder.Entity<RoleMenu>(entity =>
            {
                entity.ToTable("rolemenus");

                entity.HasKey(rm => new { rm.RoleId, rm.MenuId });

                entity.HasOne(rm => rm.Role)
                      .WithMany(r => r.RoleMenus)
                      .HasForeignKey(rm => rm.RoleId);

                entity.HasOne(rm => rm.Menu)
                      .WithMany(m => m.RoleMenus)
                      .HasForeignKey(rm => rm.MenuId);
            });

            // --- Asset (INT PK) ---
            modelBuilder.Entity<Asset>(entity =>
            {
                entity.ToTable("assets");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).HasColumnName("id").ValueGeneratedOnAdd();
                // ... Asset 的其他属性映射 ...
                // 与 AssetType, Location, Department 等核心实体的关系
                entity.HasOne(e => e.AssetType).WithMany(a => a.Assets).HasForeignKey(e => e.AssetTypeId).OnDelete(DeleteBehavior.Restrict);
                entity.HasOne(e => e.Location).WithMany(l => l.Assets).HasForeignKey(e => e.LocationId).OnDelete(DeleteBehavior.SetNull);
                entity.HasOne(e => e.Department).WithMany(d => d.Assets).HasForeignKey(e => e.DepartmentId).OnDelete(DeleteBehavior.SetNull);
                // ... Ignore NotMapped properties ...
                entity.Ignore(e => e.AssetTypeName);
                entity.Ignore(e => e.LocationName);
                // ... etc ...
            });

            // --- Location (INT PK) ---
             modelBuilder.Entity<Location>(entity =>
            {
                entity.ToTable("locations");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).HasColumnName("id").ValueGeneratedOnAdd();
                 // ... Location 的其他属性映射和关系 ...
                entity.HasOne(e => e.Parent).WithMany(e => e.Children).HasForeignKey(e => e.ParentId).OnDelete(DeleteBehavior.Restrict);
                entity.HasOne(e => e.Department).WithMany().HasForeignKey(e => e.DefaultDepartmentId).OnDelete(DeleteBehavior.SetNull); // Assuming Department entity exists
                entity.HasOne(e => e.Manager).WithMany().HasForeignKey(e => e.ManagerId).OnDelete(DeleteBehavior.SetNull); // Assuming User entity for Manager

                entity.Ignore(l => l.LocationHistories); // ADDED THIS LINE
            });

            // --- 其他核心实体配置 (AssetType, Department, etc.) ---
             modelBuilder.Entity<AssetType>(entity =>
             {
                 entity.ToTable("assettypes");
                entity.HasKey(e => e.Id);
                 entity.Property(e => e.Id).HasColumnName("id").ValueGeneratedOnAdd();
                 // ... other AssetType configurations ...
             });
             modelBuilder.Entity<Department>(entity =>
             {
                 entity.ToTable("departments");
                entity.HasKey(e => e.Id);
                 entity.Property(e => e.Id).HasColumnName("id").ValueGeneratedOnAdd();
                 // ... other Department configurations ...
             });
             // ... Add configurations for ALL other core entities ...

            // --- LocationUser (Composite PK, Join Table for Location and Personnel) ---
            modelBuilder.Entity<CoreLocationUser>(entity => // 使用别名
            {
                entity.ToTable("locationusers"); // 确保表名与 DDL 和实体一致

                // 1. 配置复合主键
                entity.HasKey(lu => new { 
                    lu.LocationId, // lu 现在是 CoreLocationUser 类型
                    lu.PersonnelId, 
                    lu.UserType 
                });

                // 2. 配置列属性 (确保列名与实体和数据库匹配)
                entity.Property(lu => lu.LocationId).HasColumnName("location_id").IsRequired();
                entity.Property(lu => lu.PersonnelId).HasColumnName("personnel_id").IsRequired();
                entity.Property(lu => lu.UserType).HasColumnName("user_type").IsRequired();
                entity.Property(lu => lu.CreatedAt).HasColumnName("created_at").IsRequired();
                // UpdatedAt 和 IsActive 是 [NotMapped] 在实体中，所以不需要在这里配置

                // 3. 配置与 Location 的关系
                entity.HasOne(lu => lu.Location) // lu.Location 类型应为 CoreLocation
                      .WithMany(l => l.LocationUsers) // l 类型应为 CoreLocation, l.LocationUsers 类型应为 ICollection<CoreLocationUser>
                      .HasForeignKey(lu => lu.LocationId)
                      .OnDelete(DeleteBehavior.Restrict); // 根据 DDL `ON DELETE RESTRICT`

                // 4. 配置与 Personnel 的关系
                entity.HasOne(lu => lu.Personnel) // lu.Personnel 类型应为 CorePersonnel
                      .WithMany(p => p.LocationUsers) // p 类型应为 CorePersonnel, p.LocationUsers 类型应为 ICollection<CoreLocationUser>
                      .HasForeignKey(lu => lu.PersonnelId)
                      .OnDelete(DeleteBehavior.Restrict); // 根据 DDL `ON DELETE RESTRICT`
            });

            // --- LocationHistory (INT PK) ---
            modelBuilder.Entity<LocationHistory>(entity =>
            {
                entity.ToTable("locationhistories"); 
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).HasColumnName("Id").ValueGeneratedOnAdd();

                entity.Property(e => e.AssetId).HasColumnName("AssetId").IsRequired();
                entity.Property(e => e.OldLocationId).HasColumnName("OldLocationId");
                entity.Property(e => e.NewLocationId).HasColumnName("NewLocationId").IsRequired();
                entity.Property(e => e.OperatorId).HasColumnName("OperatorId").IsRequired();
                entity.Property(e => e.ChangeType).HasColumnName("ChangeType").IsRequired();
                entity.Property(e => e.Notes).HasColumnName("Notes");
                entity.Property(e => e.ChangeTime).HasColumnName("ChangeTime").IsRequired();
                entity.Property(e => e.CreatedAt).HasColumnName("CreatedAt").IsRequired();

                // 关系: LocationHistory.OldLocation -> Location
                entity.HasOne(lh => lh.OldLocation)
                      .WithMany() 
                      .HasForeignKey(lh => lh.OldLocationId)
                      .OnDelete(DeleteBehavior.Restrict); 

                // 关系: LocationHistory.NewLocation -> Location
                entity.HasOne(lh => lh.NewLocation)
                      .WithMany() 
                      .HasForeignKey(lh => lh.NewLocationId)
                      .OnDelete(DeleteBehavior.Restrict); 

                // 关系: LocationHistory.Asset -> Asset
                entity.HasOne(lh => lh.Asset)
                      .WithMany(a => a.LocationHistories) 
                      .HasForeignKey(lh => lh.AssetId)
                      .OnDelete(DeleteBehavior.Cascade); 

                // 关系: LocationHistory.Operator -> User
                entity.HasOne(lh => lh.Operator)
                      .WithMany() 
                      .HasForeignKey(lh => lh.OperatorId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            modelBuilder.Entity<Permission>(entity =>
            {
                entity.ToTable("permissions");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).HasColumnName("id").ValueGeneratedOnAdd();
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Code).IsRequired().HasMaxLength(100);
                entity.HasIndex(e => e.Code).IsUnique();
                // ... other Permission configurations ...
            });

            modelBuilder.Entity<RolePermission>(entity =>
            {
                entity.ToTable("rolepermissions");
                entity.HasKey(rp => new { rp.RoleId, rp.PermissionId });
                entity.HasOne(rp => rp.Role).WithMany(r => r.RolePermissions).HasForeignKey(rp => rp.RoleId);
                entity.HasOne(rp => rp.Permission).WithMany(p => p.RolePermissions).HasForeignKey(rp => rp.PermissionId);
            });

            // --- PurchaseOrder (INT PK) ---
            modelBuilder.Entity<PurchaseOrder>(entity =>
            {
                entity.ToTable("purchaseorders");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).HasColumnName("Id").ValueGeneratedOnAdd();

                // 字段映射 - 匹配数据库字段名
                entity.Property(e => e.OrderNumber).HasColumnName("OrderCode");
                entity.Property(e => e.Title).HasColumnName("Title");
                entity.Property(e => e.Description).HasColumnName("Description");
                entity.Property(e => e.SupplierId).HasColumnName("SupplierId");
                entity.Property(e => e.Status).HasColumnName("Status");
                entity.Property(e => e.ExpectedDeliveryDate).HasColumnName("EstimatedDeliveryDate");
                entity.Property(e => e.ActualDeliveryDate).HasColumnName("ActualDeliveryDate");
                entity.Property(e => e.RequesterId).HasColumnName("ApplicantId");
                entity.Property(e => e.ApplicationTime).HasColumnName("ApplicationTime");
                entity.Property(e => e.ApproverId).HasColumnName("ApproverId");
                entity.Property(e => e.ApprovalTime).HasColumnName("ApprovalTime");
                entity.Property(e => e.TotalAmount).HasColumnName("TotalAmount");
                entity.Property(e => e.Notes).HasColumnName("Notes");
                entity.Property(e => e.CreatedAt).HasColumnName("CreatedAt");
                entity.Property(e => e.UpdatedAt).HasColumnName("UpdatedAt");

                // 配置导航属性关系
                entity.HasOne(p => p.Supplier)
                      .WithMany(s => s.PurchaseOrders)
                      .HasForeignKey(p => p.SupplierId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(p => p.Requester)
                      .WithMany()
                      .HasForeignKey(p => p.RequesterId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(p => p.Approver)
                      .WithMany()
                      .HasForeignKey(p => p.ApproverId)
                      .OnDelete(DeleteBehavior.SetNull);
            });

            // --- PurchaseItem (INT PK) ---
            modelBuilder.Entity<PurchaseItem>(entity =>
            {
                entity.ToTable("purchaseitems");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).HasColumnName("Id").ValueGeneratedOnAdd();

                // 字段映射 - 匹配数据库字段名
                entity.Property(e => e.PurchaseOrderId).HasColumnName("PurchaseOrderId");
                entity.Property(e => e.ItemName).HasColumnName("ItemName");
                entity.Property(e => e.ItemCode).HasColumnName("ItemCode");
                entity.Property(e => e.Specification).HasColumnName("Specification");
                entity.Property(e => e.AssetTypeId).HasColumnName("AssetTypeId");
                entity.Property(e => e.UnitPrice).HasColumnName("UnitPrice");
                entity.Property(e => e.Quantity).HasColumnName("Quantity");
                entity.Property(e => e.TotalPrice).HasColumnName("TotalPrice");
                entity.Property(e => e.Notes).HasColumnName("Notes");
                entity.Property(e => e.CreatedAt).HasColumnName("CreatedAt");
                entity.Property(e => e.UpdatedAt).HasColumnName("UpdatedAt");

                // 关系配置
                entity.HasOne(pi => pi.PurchaseOrder)
                      .WithMany(po => po.PurchaseItems)
                      .HasForeignKey(pi => pi.PurchaseOrderId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(pi => pi.AssetType)
                      .WithMany()
                      .HasForeignKey(pi => pi.AssetTypeId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // --- Supplier (INT PK) ---
            modelBuilder.Entity<Supplier>(entity =>
            {
                entity.ToTable("suppliers");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).HasColumnName("Id").ValueGeneratedOnAdd();
            });

            // --- FaultRecord (INT PK) ---
            modelBuilder.Entity<FaultRecord>(entity =>
            {
                entity.ToTable("faultrecords");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).HasColumnName("Id").ValueGeneratedOnAdd();

                // 明确配置所有属性的列名
                entity.Property(e => e.AssetId).HasColumnName("AssetId");
                entity.Property(e => e.FaultTypeId).HasColumnName("FaultTypeId");
                entity.Property(e => e.Title).HasColumnName("Title");
                entity.Property(e => e.Description).HasColumnName("Description");
                entity.Property(e => e.LocationId).HasColumnName("LocationId");
                entity.Property(e => e.ReporterId).HasColumnName("ReporterId");
                entity.Property(e => e.ReportTime).HasColumnName("ReportTime");
                entity.Property(e => e.Status).HasColumnName("Status");
                entity.Property(e => e.Severity).HasColumnName("Severity");
                entity.Property(e => e.AssigneeId).HasColumnName("AssigneeId");
                entity.Property(e => e.AssignTime).HasColumnName("AssignTime");
                entity.Property(e => e.ResponseTime).HasColumnName("ResponseTime");
                entity.Property(e => e.ResolutionTime).HasColumnName("ResolutionTime");
                entity.Property(e => e.Resolution).HasColumnName("Resolution");
                entity.Property(e => e.RootCause).HasColumnName("RootCause");
                entity.Property(e => e.IsReturned).HasColumnName("IsReturned");
                entity.Property(e => e.Notes).HasColumnName("Notes");
                entity.Property(e => e.CreatedAt).HasColumnName("CreatedAt");
                entity.Property(e => e.UpdatedAt).HasColumnName("UpdatedAt");

                // 关系配置
                entity.HasOne(fr => fr.Asset)
                      .WithMany(a => a.FaultRecords)
                      .HasForeignKey(fr => fr.AssetId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(fr => fr.FaultType)
                      .WithMany(ft => ft.FaultRecords)
                      .HasForeignKey(fr => fr.FaultTypeId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(fr => fr.Location)
                      .WithMany()
                      .HasForeignKey(fr => fr.LocationId)
                      .OnDelete(DeleteBehavior.SetNull);

                entity.HasOne(fr => fr.Reporter)
                      .WithMany()
                      .HasForeignKey(fr => fr.ReporterId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(fr => fr.Assignee)
                      .WithMany()
                      .HasForeignKey(fr => fr.AssigneeId)
                      .OnDelete(DeleteBehavior.SetNull);
            });

            // --- ReturnToFactory (INT PK) ---
            modelBuilder.Entity<ReturnToFactory>(entity =>
            {
                entity.ToTable("returntofactories");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).HasColumnName("Id").ValueGeneratedOnAdd();

                // 关系配置
                entity.HasOne(rtf => rtf.Asset)
                      .WithMany()
                      .HasForeignKey(rtf => rtf.AssetId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(rtf => rtf.Supplier)
                      .WithMany()
                      .HasForeignKey(rtf => rtf.SupplierId)
                      .OnDelete(DeleteBehavior.Restrict);

                // 注意：SenderId字段对应数据库中的SenderId字段，暂时不配置导航属性
            });
        }

        private void ConfigureTasksModule(ModelBuilder modelBuilder)
        {
            // --- Task (BIGINT PK) ---
            modelBuilder.Entity<Task>(entity =>
            {
                entity.ToTable("tasks");
                entity.HasKey(e => e.TaskId);
                entity.Property(e => e.TaskId).HasColumnName("TaskId").ValueGeneratedOnAdd();
                entity.Property(e => e.ProjectId).HasColumnName("ProjectId");
                entity.Property(e => e.ParentTaskId).HasColumnName("ParentTaskId");
                entity.Property(e => e.CreatorUserId).HasColumnName("CreatorUserId");
                entity.Property(e => e.AssigneeUserId).HasColumnName("AssigneeUserId");
                entity.Property(e => e.AssetId).HasColumnName("AssetId");
                entity.Property(e => e.LocationId).HasColumnName("LocationId");
                entity.Property(e => e.Name).HasColumnName("Name");
                entity.Property(e => e.Description).HasColumnName("Description");
                entity.Property(e => e.Status).HasColumnName("Status");
                entity.Property(e => e.Priority).HasColumnName("Priority");
                entity.Property(e => e.TaskType).HasColumnName("TaskType");
                entity.Property(e => e.PlanStartDate).HasColumnName("PlanStartDate");
                entity.Property(e => e.PlanEndDate).HasColumnName("PlanEndDate");
                entity.Property(e => e.ActualStartDate).HasColumnName("ActualStartDate");
                entity.Property(e => e.ActualEndDate).HasColumnName("ActualEndDate");
                entity.Property(e => e.CreationTimestamp).HasColumnName("CreationTimestamp");
                entity.Property(e => e.LastUpdatedTimestamp).HasColumnName("LastUpdatedTimestamp");
                entity.Property(e => e.IsOverdueAcknowledged).HasColumnName("IsOverdueAcknowledged");
                entity.Property(e => e.PDCAStage).HasColumnName("PDCAStage");
                entity.Property(e => e.PreviousInstanceTaskId).HasColumnName("PreviousInstanceTaskId");
                entity.Property(e => e.PeriodicTaskScheduleId).HasColumnName("PeriodicTaskScheduleId");
                entity.Property(e => e.Progress).HasColumnName("Progress");
                entity.Property(e => e.Points).HasColumnName("Points");

                // Relationships
                entity.HasOne(t => t.Creator).WithMany().HasForeignKey(t => t.CreatorUserId).OnDelete(DeleteBehavior.Restrict);
                entity.HasOne(t => t.Assignee).WithMany().HasForeignKey(t => t.AssigneeUserId).OnDelete(DeleteBehavior.SetNull);
                entity.HasOne(t => t.ParentTask).WithMany(t => t.SubTasks).HasForeignKey(t => t.ParentTaskId).OnDelete(DeleteBehavior.SetNull);
                entity.HasOne(t => t.Asset).WithMany().HasForeignKey(t => t.AssetId).OnDelete(DeleteBehavior.SetNull);
                entity.HasOne(t => t.Location).WithMany().HasForeignKey(t => t.LocationId).OnDelete(DeleteBehavior.SetNull);
                entity.HasOne(t => t.PreviousInstanceTask).WithOne().HasForeignKey<Task>(t => t.PreviousInstanceTaskId).OnDelete(DeleteBehavior.SetNull);
                
                // 忽略NotMapped的双向导航属性，但保留外键字段
                entity.Ignore(t => t.PeriodicTaskSchedule);
                entity.Ignore(t => t.TemplateForSchedules);
                
                // 配置与PeriodicTaskSchedule的单向外键关系
                entity.HasOne<PeriodicTaskSchedule>()
                      .WithMany()
                      .HasForeignKey(t => t.PeriodicTaskScheduleId)
                      .OnDelete(DeleteBehavior.SetNull);
            });

            // --- Comment (BIGINT PK) ---
            modelBuilder.Entity<Comment>(entity =>
            {
                entity.ToTable("comments");
                entity.HasKey(e => e.CommentId);
                entity.Property(e => e.CommentId).HasColumnName("CommentId").ValueGeneratedOnAdd();
                entity.Property(e => e.TaskId).HasColumnName("TaskId").IsRequired();
                entity.Property(e => e.UserId).HasColumnName("UserId").IsRequired(); // 修复：匹配数据库字段名
                entity.Property(e => e.ParentCommentId).HasColumnName("ParentCommentId");
                entity.Property(e => e.Content).HasColumnName("Content").IsRequired();
                entity.Property(e => e.MentionedUserIds).HasColumnName("MentionedUserIds");
                entity.Property(e => e.IsPinned).HasColumnName("IsPinned").IsRequired();
                entity.Property(e => e.IsEdited).HasColumnName("IsEdited").IsRequired(); // 修复：匹配数据库字段名
                entity.Property(e => e.CreationTimestamp).HasColumnName("CreationTimestamp").IsRequired();
                entity.Property(e => e.LastUpdatedTimestamp).HasColumnName("LastUpdatedTimestamp").IsRequired();


                // Relationships
                entity.HasOne(c => c.Task).WithMany(t => t.Comments).HasForeignKey(c => c.TaskId).OnDelete(DeleteBehavior.Cascade);
                entity.HasOne(c => c.User).WithMany().HasForeignKey(c => c.UserId).OnDelete(DeleteBehavior.Restrict);
                entity.HasOne(c => c.ParentComment).WithMany(c => c.Replies).HasForeignKey(c => c.ParentCommentId).OnDelete(DeleteBehavior.Cascade);
            });

            // --- Attachment (BIGINT PK) ---
            modelBuilder.Entity<Attachment>(entity =>
            {
                entity.ToTable("attachments");
                entity.HasKey(e => e.AttachmentId);
                entity.Property(e => e.AttachmentId).HasColumnName("AttachmentId").ValueGeneratedOnAdd();
                entity.Property(e => e.TaskId).HasColumnName("TaskId");
                entity.Property(e => e.CommentId).HasColumnName("CommentId");
                entity.Property(e => e.UploaderUserId).HasColumnName("UploaderUserId").IsRequired(); // 修复：匹配数据库字段名
                entity.Property(e => e.FileName).HasColumnName("FileName").IsRequired();
                entity.Property(e => e.StoredFileName).HasColumnName("StoredFileName").IsRequired();
                entity.Property(e => e.FilePath).HasColumnName("FilePath").IsRequired();
                entity.Property(e => e.FileSize).HasColumnName("FileSize").IsRequired();
                entity.Property(e => e.FileType).HasColumnName("FileType").IsRequired();
                entity.Property(e => e.IsPreviewable).HasColumnName("IsPreviewable").IsRequired();
                entity.Property(e => e.StorageType).HasColumnName("StorageType").IsRequired();
                entity.Property(e => e.CreationTimestamp).HasColumnName("CreationTimestamp").IsRequired();

                // Relationships
                entity.HasOne(a => a.Task).WithMany(t => t.Attachments).HasForeignKey(a => a.TaskId).OnDelete(DeleteBehavior.Cascade);
                entity.HasOne(a => a.Comment).WithMany(c => c.Attachments).HasForeignKey(a => a.CommentId).OnDelete(DeleteBehavior.Cascade);
                entity.HasOne(a => a.UploaderUser).WithMany().HasForeignKey(a => a.UploaderUserId).OnDelete(DeleteBehavior.Restrict);
            });

             // --- TaskHistory (BIGINT PK) ---
            modelBuilder.Entity<TaskHistory>(entity =>
            {
                entity.ToTable("taskhistories"); // 使用复数形式的表名
                entity.HasKey(e => e.TaskHistoryId);
                entity.Property(e => e.TaskHistoryId).HasColumnName("task_history_id").ValueGeneratedOnAdd();
                entity.Property(e => e.TaskId).HasColumnName("task_id");
                entity.Property(e => e.UserId).HasColumnName("user_id");
                entity.Property(e => e.Timestamp).HasColumnName("timestamp");
                entity.Property(e => e.ActionType).HasColumnName("action_type");
                entity.Property(e => e.FieldName).HasColumnName("field_name");
                entity.Property(e => e.OldValue).HasColumnName("old_value");
                entity.Property(e => e.NewValue).HasColumnName("new_value");
                entity.Property(e => e.CommentId).HasColumnName("comment_id");
                entity.Property(e => e.AttachmentId).HasColumnName("attachment_id");
                entity.Property(e => e.Description).HasColumnName("description");

                // Relationships
                entity.HasOne(th => th.Task).WithMany(t => t.History).HasForeignKey(th => th.TaskId).OnDelete(DeleteBehavior.Cascade);
                entity.HasOne(th => th.User).WithMany().HasForeignKey(th => th.UserId).OnDelete(DeleteBehavior.SetNull);
                entity.HasOne(th => th.Comment).WithMany().HasForeignKey(th => th.CommentId).OnDelete(DeleteBehavior.SetNull);
                entity.HasOne(th => th.Attachment).WithMany().HasForeignKey(th => th.AttachmentId).OnDelete(DeleteBehavior.SetNull);

            });

            // --- TaskAssignee (BIGINT PK) ---
            modelBuilder.Entity<TaskAssignee>(entity =>
            {
                entity.ToTable("taskassignees"); // 数据库表名为 taskassignees (全小写)
                entity.HasKey(e => e.TaskAssigneeId);
                entity.Property(e => e.TaskAssigneeId).HasColumnName("task_assignee_id").ValueGeneratedOnAdd();
                entity.Property(e => e.TaskId).HasColumnName("task_id");
                entity.Property(e => e.UserId).HasColumnName("user_id");
                entity.Property(e => e.AssignmentType).HasColumnName("assignment_type");
                entity.Property(e => e.AssignedByUserId).HasColumnName("assigned_by_user_id");
                entity.Property(e => e.AssignmentTimestamp).HasColumnName("assignment_timestamp");

                // Relationships
                entity.HasOne(ta => ta.Task).WithMany(t => t.Assignees).HasForeignKey(ta => ta.TaskId).OnDelete(DeleteBehavior.Cascade);
                entity.HasOne(ta => ta.User).WithMany().HasForeignKey(ta => ta.UserId).OnDelete(DeleteBehavior.Cascade);
                entity.HasOne(ta => ta.AssignedByUser).WithMany().HasForeignKey(ta => ta.AssignedByUserId).OnDelete(DeleteBehavior.Restrict);
            });

             // --- PeriodicTaskSchedule (BIGINT PK) ---
            modelBuilder.Entity<PeriodicTaskSchedule>(entity =>
            {
                entity.ToTable("PeriodicTaskSchedules"); // 使用数据库中实际存在的表名（Pascal命名）
                entity.HasKey(e => e.PeriodicTaskScheduleId);
                entity.Property(e => e.PeriodicTaskScheduleId)
                    .HasColumnName("PeriodicTaskScheduleId")
                    .ValueGeneratedOnAdd();
                entity.Property(e => e.TemplateTaskId).HasColumnName("TemplateTaskId");

                // 使用Pascal命名匹配数据库实际列名
                entity.Property(e => e.CreatorUserId).HasColumnName("CreatorUserId");

                entity.Property(e => e.Name).HasColumnName("Name");
                entity.Property(e => e.Description).HasColumnName("Description");
                entity.Property(e => e.RecurrenceType).HasColumnName("RecurrenceType");
                entity.Property(e => e.RecurrenceInterval).HasColumnName("RecurrenceInterval");
                entity.Property(e => e.DaysOfWeek).HasColumnName("DaysOfWeek");
                entity.Property(e => e.DayOfMonth).HasColumnName("DayOfMonth");
                entity.Property(e => e.WeekOfMonth).HasColumnName("WeekOfMonth");
                entity.Property(e => e.DayOfWeekForMonth).HasColumnName("DayOfWeekForMonth");
                entity.Property(e => e.MonthOfYear).HasColumnName("MonthOfYear");
                entity.Property(e => e.CronExpression).HasColumnName("CronExpression");
                entity.Property(e => e.StartDate).HasColumnName("StartDate");
                entity.Property(e => e.EndConditionType).HasColumnName("EndConditionType");
                entity.Property(e => e.EndDate).HasColumnName("EndDate");
                entity.Property(e => e.TotalOccurrences).HasColumnName("TotalOccurrences");
                entity.Property(e => e.OccurrencesGenerated).HasColumnName("OccurrencesGenerated");
                entity.Property(e => e.NextGenerationTime).HasColumnName("NextGenerationTime");
                entity.Property(e => e.Status).HasColumnName("Status");
                entity.Property(e => e.LastGeneratedTimestamp).HasColumnName("LastGeneratedTimestamp");
                entity.Property(e => e.LastError).HasColumnName("LastError");
                entity.Property(e => e.DefaultPoints).HasColumnName("DefaultPoints");
                entity.Property(e => e.CreationTimestamp).HasColumnName("CreationTimestamp");
                entity.Property(e => e.LastUpdatedTimestamp).HasColumnName("LastUpdatedTimestamp");

                // Relationships - 明确指定外键映射，避免EF Core自动生成额外列
                entity.HasOne(pts => pts.TemplateTask)
                      .WithMany()
                      .HasForeignKey(pts => pts.TemplateTaskId)
                      .HasPrincipalKey(t => t.TaskId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(pts => pts.CreatorUser)
                      .WithMany()
                      .HasForeignKey(pts => pts.CreatorUserId)
                      .OnDelete(DeleteBehavior.Restrict);

                // 配置与负责人的一对多关系
                entity.HasMany(pts => pts.Assignees)
                      .WithOne(a => a.PeriodicTaskSchedule)
                      .HasForeignKey(a => a.PeriodicTaskScheduleId)
                      .OnDelete(DeleteBehavior.Cascade);
                      
                // 忽略NotMapped的导航属性
                entity.Ignore(pts => pts.GeneratedTasks);
            });

            // --- PeriodicTaskScheduleAssignee ---
            modelBuilder.Entity<PeriodicTaskScheduleAssignee>(entity =>
            {
                entity.ToTable("PeriodicTaskScheduleAssignees");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).HasColumnName("Id").ValueGeneratedOnAdd();
                entity.Property(e => e.PeriodicTaskScheduleId).HasColumnName("PeriodicTaskScheduleId");
                entity.Property(e => e.UserId).HasColumnName("UserId");
                entity.Property(e => e.CreatedAt).HasColumnName("CreatedAt");

                // 创建唯一索引，防止重复添加同一用户
                entity.HasIndex(e => new { e.PeriodicTaskScheduleId, e.UserId })
                      .IsUnique()
                      .HasDatabaseName("uk_schedule_user");

                // 创建普通索引提高查询性能
                entity.HasIndex(e => e.PeriodicTaskScheduleId).HasDatabaseName("idx_schedule_id");
                entity.HasIndex(e => e.UserId).HasDatabaseName("idx_user_id");
            });

            // --- PdcaPlan (BIGINT PK) ---
            modelBuilder.Entity<PdcaPlan>(entity =>
            {
                entity.ToTable("pdcaplans"); // 数据库表名为 pdcaplans (全小写)
                entity.HasKey(e => e.PdcaPlanId);
                entity.Property(e => e.PdcaPlanId).HasColumnName("pdca_plan_id").ValueGeneratedOnAdd();
                entity.Property(e => e.TaskId).HasColumnName("TaskId");
                entity.Property(e => e.CreatorUserId).HasColumnName("creator_user_id"); // 确保映射
                entity.Property(e => e.ResponsiblePersonId).HasColumnName("responsible_person_id"); // 确保映射
                entity.Property(e => e.Title).HasColumnName("title");
                entity.Property(e => e.Stage).HasColumnName("stage");
                entity.Property(e => e.Goal).HasColumnName("goal");
                entity.Property(e => e.PlanContent).HasColumnName("plan_content");
                entity.Property(e => e.DoRecord).HasColumnName("do_record");
                entity.Property(e => e.CheckResult).HasColumnName("check_result");
                entity.Property(e => e.ActAction).HasColumnName("act_action");
                entity.Property(e => e.Status).HasColumnName("status");
                entity.Property(e => e.CompletionRate).HasColumnName("completion_rate");
                entity.Property(e => e.Notes).HasColumnName("notes");
                entity.Property(e => e.CreationTimestamp).HasColumnName("creation_timestamp");
                entity.Property(e => e.LastUpdatedTimestamp).HasColumnName("last_updated_timestamp");

                // Relationships
                entity.HasOne(p => p.Task).WithOne().HasForeignKey<PdcaPlan>(p => p.TaskId).OnDelete(DeleteBehavior.Cascade);
                entity.HasOne(p => p.CreatorUser).WithMany().HasForeignKey(p => p.CreatorUserId).OnDelete(DeleteBehavior.Restrict);
                entity.HasOne(p => p.ResponsiblePerson).WithMany().HasForeignKey(p => p.ResponsiblePersonId).OnDelete(DeleteBehavior.Restrict);
            });
        }

        private void ConfigureNotesModule(ModelBuilder modelBuilder)
        {
            // --- QuickMemo ---
            modelBuilder.Entity<QuickMemo>(entity =>
            {
                entity.ToTable("quick_memos");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).HasColumnName("id").ValueGeneratedOnAdd();

                entity.Property(e => e.Title).HasColumnName("title").HasMaxLength(200).IsRequired();
                entity.Property(e => e.Content).HasColumnName("content").HasColumnType("TEXT");
                entity.Property(e => e.UserId).HasColumnName("user_id").IsRequired();
                entity.Property(e => e.CategoryId).HasColumnName("category_id");
                entity.Property(e => e.IsPinned).HasColumnName("is_pinned").IsRequired().HasDefaultValue(false);
                entity.Property(e => e.Color).HasColumnName("color").HasMaxLength(7);
                entity.Property(e => e.CreatedAt).HasColumnName("created_at").IsRequired();
                entity.Property(e => e.UpdatedAt).HasColumnName("updated_at").IsRequired();
                
                // 配置使用PascalCase列名的属性
                entity.Property(e => e.PositionXDb).HasColumnName("PositionX");
                entity.Property(e => e.PositionYDb).HasColumnName("PositionY");
                entity.Property(e => e.SizeWidth).HasColumnName("SizeWidth");
                entity.Property(e => e.SizeHeight).HasColumnName("SizeHeight");
                entity.Property(e => e.ZIndex).HasColumnName("ZIndex");

                entity.HasOne(qm => qm.User)
                      .WithMany()
                      .HasForeignKey(qm => qm.UserId) 
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(qm => qm.Category)
                      .WithMany(qc => qc.QuickMemos)
                      .HasForeignKey(qm => qm.CategoryId)
                      .OnDelete(DeleteBehavior.SetNull);

                entity.HasIndex(e => e.UserId).HasDatabaseName("ix_quick_memos_user_id");
                entity.HasIndex(e => new { e.UserId, e.IsPinned, e.UpdatedAt }).HasDatabaseName("ix_quick_memos_user_pinned_updated");
                entity.HasIndex(e => new { e.UserId, e.CategoryId }).HasDatabaseName("ix_quick_memos_user_category");
            });

            // --- QuickMemoCategory ---
            modelBuilder.Entity<QuickMemoCategory>(entity =>
            {
                entity.ToTable("quick_memo_categories");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).HasColumnName("id").ValueGeneratedOnAdd();

                entity.Property(e => e.Name).HasColumnName("name").HasMaxLength(100).IsRequired();
                entity.Property(e => e.Color).HasColumnName("color").HasMaxLength(7);
                entity.Property(e => e.UserId).HasColumnName("user_id").IsRequired();
                entity.Property(e => e.CreatedAt).HasColumnName("created_at").IsRequired();
                entity.Property(e => e.UpdatedAt).HasColumnName("updated_at").IsRequired();

                entity.HasOne(qmc => qmc.User)
                      .WithMany()
                      .HasForeignKey(qmc => qmc.UserId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(e => new { e.UserId, e.Name }).IsUnique().HasDatabaseName("ix_quick_memo_categories_user_name");
            });
        }

        private void ConfigureSparePartsModule(ModelBuilder modelBuilder)
        {
            // 配置备品备件实体
            modelBuilder.Entity<SparePart>(entity =>
            {
                entity.ToTable("spare_parts");
                entity.HasKey(e => e.Id);
                
                // 与类型的关系
                entity.HasOne(e => e.Type)
                    .WithMany(t => t.SpareParts)
                    .HasForeignKey(e => e.TypeId)
                    .OnDelete(DeleteBehavior.Restrict);
                
                // 与库位的关系
                entity.HasOne(e => e.Location)
                    .WithMany(l => l.SpareParts)
                    .HasForeignKey(e => e.LocationId)
                    .OnDelete(DeleteBehavior.SetNull);
                
                // 创建索引
                entity.HasIndex(e => e.Code).IsUnique();
                entity.HasIndex(e => e.TypeId);
                entity.HasIndex(e => e.LocationId);
                entity.HasIndex(e => e.StockQuantity);
            });
            
            // 配置备品备件类型实体
            modelBuilder.Entity<SparePartType>(entity =>
            {
                entity.ToTable("spare_part_types");
                entity.HasKey(e => e.Id);
                
                // 明确配置列名
                entity.Property(e => e.Id).HasColumnName("id");
                entity.Property(e => e.Name).HasColumnName("name").IsRequired().HasMaxLength(50);
                entity.Property(e => e.Code).HasColumnName("code").IsRequired().HasMaxLength(20);
                entity.Property(e => e.ParentId).HasColumnName("parent_id");
                entity.Property(e => e.Description).HasColumnName("description").HasMaxLength(200);
                entity.Property(e => e.CreatedAt).HasColumnName("created_at");
                entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");
                entity.Property(e => e.Path).HasColumnName("path").HasMaxLength(255);
                entity.Property(e => e.Level).HasColumnName("level");
                
                // 与父类型的关系
                entity.HasOne(e => e.Parent)
                    .WithMany(t => t.Children)
                    .HasForeignKey(e => e.ParentId)
                    .OnDelete(DeleteBehavior.Restrict);
                
                // 创建索引
                entity.HasIndex(e => e.Code).IsUnique();
                entity.HasIndex(e => e.ParentId);
                entity.HasIndex(e => e.Path);
            });
            
            // 配置备品备件库位实体
            modelBuilder.Entity<SparePartLocation>(entity =>
            {
                entity.ToTable("spare_part_locations");
                entity.HasKey(e => e.Id);
                
                // 明确配置列名
                entity.Property(e => e.Id).HasColumnName("id");
                entity.Property(e => e.Code).HasColumnName("code").IsRequired().HasMaxLength(50);
                entity.Property(e => e.Name).HasColumnName("name").IsRequired().HasMaxLength(100);
                entity.Property(e => e.Area).HasColumnName("area").IsRequired().HasMaxLength(20);
                entity.Property(e => e.Description).HasColumnName("description").HasMaxLength(200);
                entity.Property(e => e.CreatedAt).HasColumnName("created_at");
                entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");
                entity.Property(e => e.IsActive).HasColumnName("is_active");
                
                // 创建索引
                entity.HasIndex(e => e.Code).IsUnique();
                entity.HasIndex(e => e.Area);
            });
            
            // 配置备品备件出入库记录实体
            modelBuilder.Entity<SparePartTransaction>(entity =>
            {
                entity.ToTable("spare_part_transactions");
                entity.HasKey(e => e.Id);
                
                // 与备件的关系
                entity.HasOne(e => e.Part)
                    .WithMany(p => p.Transactions)
                    .HasForeignKey(e => e.PartId)
                    .OnDelete(DeleteBehavior.Restrict);
                
                // 与库位的关系
                entity.HasOne(e => e.Location)
                    .WithMany(l => l.Transactions)
                    .HasForeignKey(e => e.LocationId)
                    .OnDelete(DeleteBehavior.Restrict);
                
                // 创建索引
                entity.HasIndex(e => e.PartId);
                entity.HasIndex(e => e.LocationId);
                entity.HasIndex(e => e.Type);
                entity.HasIndex(e => e.OperationTime);
                entity.HasIndex(e => e.BatchNumber);
            });
        }

        /* // Gamification Module Configuration - Commented out
        private void ConfigureGamificationModule(ModelBuilder modelBuilder)
        {
             // --- GamificationBadge (BIGINT PK) ---
            modelBuilder.Entity<GamificationBadge>(entity =>
            {
                entity.ToTable("gamification_badges");
                entity.HasKey(e => e.BadgeId);
                entity.Property(e => e.BadgeId).HasColumnName("badge_id").ValueGeneratedOnAdd();
                entity.Property(e => e.Name).HasColumnName("name").HasMaxLength(100).IsRequired();
                entity.Property(e => e.Description).HasColumnName("description").HasColumnType("TEXT");
                entity.Property(e => e.IconUrl).HasColumnName("icon_url").HasMaxLength(255);
                entity.Property(e => e.Criteria).HasColumnName("criteria").HasColumnType("TEXT");
                entity.Property(e => e.PointsAwarded).HasColumnName("points_awarded").HasDefaultValue(0);
                entity.Property(e => e.IsEnabled).HasColumnName("is_enabled").HasDefaultValue(true);
                entity.Property(e => e.CreationTimestamp).HasColumnName("creation_timestamp").IsRequired();
            });

            // --- GamificationItem (BIGINT PK) ---
            modelBuilder.Entity<GamificationItem>(entity =>
            {
                entity.ToTable("gamification_items");
                entity.HasKey(e => e.ItemId);
                entity.Property(e => e.ItemId).HasColumnName("item_id").ValueGeneratedOnAdd();
                entity.Property(e => e.Name).HasColumnName("name").HasMaxLength(100).IsRequired();
                entity.Property(e => e.Description).HasColumnName("description").HasColumnType("TEXT");
                entity.Property(e => e.IconUrl).HasColumnName("icon_url").HasMaxLength(255);
                entity.Property(e => e.ItemType).HasColumnName("item_type").HasMaxLength(50);
                entity.Property(e => e.Cost).HasColumnName("cost").HasDefaultValue(0);
                entity.Property(e => e.EffectDescription).HasColumnName("effect_description").HasColumnType("TEXT");
                entity.Property(e => e.IsAvailable).HasColumnName("is_available").HasDefaultValue(true);
                entity.Property(e => e.CreationTimestamp).HasColumnName("creation_timestamp").IsRequired();
            });

            // --- GamificationLog (BIGINT PK) ---
            modelBuilder.Entity<GamificationLog>(entity =>
            {
                entity.ToTable("gamification_log");
                entity.HasKey(e => e.LogId);
                entity.Property(e => e.LogId).HasColumnName("log_id").ValueGeneratedOnAdd();
                entity.Property(e => e.UserId).HasColumnName("user_id").IsRequired(); // INT FK
                entity.Property(e => e.EventType).HasColumnName("event_type").HasMaxLength(100).IsRequired();
                entity.Property(e => e.PointsChange).HasColumnName("points_change").HasDefaultValue(0);
                entity.Property(e => e.RelatedTaskId).HasColumnName("related_task_id"); // BIGINT FK (Nullable)
                entity.Property(e => e.RelatedBadgeId).HasColumnName("related_badge_id"); // BIGINT FK (Nullable)
                entity.Property(e => e.RelatedItemId).HasColumnName("related_item_id"); // BIGINT FK (Nullable)
                entity.Property(e => e.Description).HasColumnName("description").HasColumnType("TEXT");
                entity.Property(e => e.Timestamp).HasColumnName("timestamp").IsRequired();

                // Relationships
                entity.HasOne<User>().WithMany().HasForeignKey(e => e.UserId).OnDelete(DeleteBehavior.Cascade); // INT FK
                entity.HasOne<Task>().WithMany().HasForeignKey(e => e.RelatedTaskId).OnDelete(DeleteBehavior.SetNull); // BIGINT FK
                entity.HasOne<GamificationBadge>().WithMany().HasForeignKey(e => e.RelatedBadgeId).OnDelete(DeleteBehavior.SetNull); // BIGINT FK
                entity.HasOne<GamificationItem>().WithMany().HasForeignKey(e => e.RelatedItemId).OnDelete(DeleteBehavior.SetNull); // BIGINT FK
            });

            // --- GamificationUserBadge (BIGINT PK) ---
            modelBuilder.Entity<GamificationUserBadge>(entity =>
            {
                entity.ToTable("gamification_userbadges"); // Schema uses 'gamification_userbadges'
                entity.HasKey(e => e.UserBadgeId);
                entity.Property(e => e.UserBadgeId).HasColumnName("user_badge_id").ValueGeneratedOnAdd();
                entity.Property(e => e.UserId).HasColumnName("user_id").IsRequired(); // INT FK
                entity.Property(e => e.BadgeId).HasColumnName("badge_id").IsRequired(); // BIGINT FK
                entity.Property(e => e.EarnedTimestamp).HasColumnName("earned_timestamp").IsRequired();
                entity.Property(e => e.IsDisplayed).HasColumnName("is_displayed").HasDefaultValue(false);

                // Relationships
                entity.HasOne<User>().WithMany().HasForeignKey(e => e.UserId).OnDelete(DeleteBehavior.Cascade); // INT FK
                entity.HasOne<GamificationBadge>().WithMany().HasForeignKey(e => e.BadgeId).OnDelete(DeleteBehavior.Cascade); // BIGINT FK

                entity.HasIndex(e => new { e.UserId, e.BadgeId }).IsUnique();
            });

             // --- GamificationUserItem (BIGINT PK) ---
            modelBuilder.Entity<GamificationUserItem>(entity =>
            {
                entity.ToTable("gamification_useritems"); // Schema uses 'gamification_useritems'
                entity.HasKey(e => e.UserItemId);
                entity.Property(e => e.UserItemId).HasColumnName("user_item_id").ValueGeneratedOnAdd();
                entity.Property(e => e.UserId).HasColumnName("user_id").IsRequired(); // INT FK
                entity.Property(e => e.ItemId).HasColumnName("item_id").IsRequired(); // BIGINT FK
                entity.Property(e => e.Quantity).HasColumnName("quantity").IsRequired();
                entity.Property(e => e.AcquiredTimestamp).HasColumnName("acquired_timestamp").IsRequired();
                entity.Property(e => e.LastUsedTimestamp).HasColumnName("last_used_timestamp");

                // Relationships
                entity.HasOne<User>().WithMany().HasForeignKey(e => e.UserId).OnDelete(DeleteBehavior.Cascade); // INT FK
                entity.HasOne<GamificationItem>().WithMany().HasForeignKey(e => e.ItemId).OnDelete(DeleteBehavior.Cascade); // BIGINT FK

                entity.HasIndex(e => new { e.UserId, e.ItemId }).IsUnique();
            });

            // --- GamificationUserStat (BIGINT PK) ---
            modelBuilder.Entity<GamificationUserStat>(entity =>
            {
                entity.ToTable("gamification_userstats"); // Schema uses 'gamification_userstats'
                entity.HasKey(e => e.UserStatId);
                entity.Property(e => e.UserStatId).HasColumnName("user_stat_id").ValueGeneratedOnAdd();
                entity.Property(e => e.UserId).HasColumnName("user_id").IsRequired(); // INT FK
                entity.Property(e => e.TotalPoints).HasColumnName("total_points").IsRequired();
                entity.Property(e => e.Level).HasColumnName("level").IsRequired();
                entity.Property(e => e.TasksCompleted).HasColumnName("tasks_completed").IsRequired();
                entity.Property(e => e.AchievementsUnlocked).HasColumnName("achievements_unlocked").IsRequired();
                entity.Property(e => e.LastActivityTimestamp).HasColumnName("last_activity_timestamp").IsRequired();
                entity.Property(e => e.LastUpdatedTimestamp).HasColumnName("last_updated_timestamp").IsRequired();

                // Relationships
                entity.HasOne<User>().WithOne().HasForeignKey<GamificationUserStat>(e => e.UserId).OnDelete(DeleteBehavior.Cascade); // INT FK
                entity.HasIndex(e => e.UserId).IsUnique();
            });

             // --- PointLeaderboard (BIGINT PK) ---
            modelBuilder.Entity<PointLeaderboard>(entity =>
            {
                entity.ToTable("point_leaderboard");
                entity.HasKey(e => e.LeaderboardId);
                entity.Property(e => e.LeaderboardId).HasColumnName("leaderboard_id").ValueGeneratedOnAdd();
                entity.Property(e => e.UserId).HasColumnName("user_id").IsRequired(); // INT FK
                entity.Property(e => e.Score).HasColumnName("score").IsRequired();
                entity.Property(e => e.Rank).HasColumnName("rank").IsRequired();
                entity.Property(e => e.PeriodType).HasColumnName("period_type").HasMaxLength(50).IsRequired();
                entity.Property(e => e.PeriodIdentifier).HasColumnName("period_identifier").HasMaxLength(50).IsRequired();
                entity.Property(e => e.LastUpdatedTimestamp).HasColumnName("last_updated_timestamp").IsRequired();

                // Relationships
                entity.HasOne<User>().WithMany().HasForeignKey(e => e.UserId).OnDelete(DeleteBehavior.Cascade); // INT FK

                entity.HasIndex(e => new { e.PeriodType, e.PeriodIdentifier, e.Rank });
                entity.HasIndex(e => new { e.PeriodType, e.PeriodIdentifier, e.UserId }).IsUnique();
            });
        }
        */

        /* // Notification Module Configuration - Commented out
        private void ConfigureNotificationModule(ModelBuilder modelBuilder)
        {
            // --- Notification (BIGINT PK) ---
            modelBuilder.Entity<Notification>(entity =>
            {
                entity.ToTable("notifications");
                entity.HasKey(e => e.NotificationId);
                entity.Property(e => e.NotificationId).HasColumnName("notification_id").ValueGeneratedOnAdd();
                entity.Property(e => e.UserId).HasColumnName("user_id").IsRequired(); // INT FK to Users
                entity.Property(e => e.Type).HasColumnName("type").HasMaxLength(100).IsRequired();
                entity.Property(e => e.Message).HasColumnName("message").HasColumnType("TEXT").IsRequired();
                entity.Property(e => e.IsRead).HasColumnName("is_read").IsRequired().HasDefaultValue(false);
                entity.Property(e => e.ReadTimestamp).HasColumnName("read_timestamp");
                entity.Property(e => e.Link).HasColumnName("link").HasMaxLength(1024);
                entity.Property(e => e.RelatedTaskId).HasColumnName("related_task_id"); // BIGINT FK (Nullable)
                entity.Property(e => e.RelatedCommentId).HasColumnName("related_comment_id"); // BIGINT FK (Nullable)
                entity.Property(e => e.RelatedUserId).HasColumnName("related_user_id"); // INT FK (Nullable, Triggering user)
                entity.Property(e => e.CreationTimestamp).HasColumnName("creation_timestamp").IsRequired();

                // Relationships
                entity.HasOne<User>().WithMany().HasForeignKey(e => e.UserId).OnDelete(DeleteBehavior.Cascade);
                entity.HasOne<Task>().WithMany().HasForeignKey(e => e.RelatedTaskId).OnDelete(DeleteBehavior.SetNull); // Related Task (BIGINT FK)
                entity.HasOne<Comment>().WithMany().HasForeignKey(e => e.RelatedCommentId).OnDelete(DeleteBehavior.SetNull); // Related Comment (BIGINT FK)
                entity.HasOne<User>().WithMany().HasForeignKey(e => e.RelatedUserId).OnDelete(DeleteBehavior.SetNull); // Triggering User (INT FK)
            });
        }
        */

        // =================== SaveChanges Overrides for Auditing ===================

        // (假设 IAuditableEntity 在 Models 命名空间)
        public override int SaveChanges(bool acceptAllChangesOnSuccess)
        {
            UpdateAuditableEntities();
            return base.SaveChanges(acceptAllChangesOnSuccess);
        }

        public override System.Threading.Tasks.Task<int> SaveChangesAsync(bool acceptAllChangesOnSuccess, CancellationToken cancellationToken = default)
        {
            UpdateAuditableEntities();
            return base.SaveChangesAsync(acceptAllChangesOnSuccess, cancellationToken);
        }

        private void UpdateAuditableEntities()
        {
            var now = DateTime.UtcNow;

            foreach (var entry in ChangeTracker.Entries<IAuditableEntity>())
            {
                if (entry.State == EntityState.Added)
                {
                    entry.Entity.CreatedAt = now;
                    entry.Entity.UpdatedAt = now;
                }
                else if (entry.State == EntityState.Modified)
                {
                    entry.Property(nameof(IAuditableEntity.CreatedAt)).IsModified = false;
                    entry.Entity.UpdatedAt = now;
                }
            }

            // Check if there was any specific handling for UserRole's timestamps outside the IAuditableEntity loop
            // For example, if it was handled by property name convention, that part should be removed or adjusted.
            // Based on the previous AppDbContext content, UserRole was likely handled by IAuditableEntity.
            // So, removing IAuditableEntity from UserRole should be sufficient.

            // ... (rest of the V2 entity timestamp handling) ...
        }

        private void ConfigureRepairModule(ModelBuilder modelBuilder)
        {
            // --- RepairOrder (INT PK) ---
            modelBuilder.Entity<RepairOrder>(entity =>
            {
                entity.ToTable("RepairOrders");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).HasColumnName("Id").ValueGeneratedOnAdd();
                
                entity.Property(e => e.OrderCode).HasColumnName("OrderCode").IsRequired().HasMaxLength(50);
                entity.Property(e => e.SupplierId).HasColumnName("SupplierId").IsRequired();
                entity.Property(e => e.SendDate).HasColumnName("SendDate");
                entity.Property(e => e.ExpectedReturnDate).HasColumnName("ExpectedReturnDate");
                entity.Property(e => e.ActualReturnDate).HasColumnName("ActualReturnDate");
                entity.Property(e => e.TotalCost).HasColumnName("TotalCost").HasColumnType("decimal(18,2)");
                entity.Property(e => e.Status).HasColumnName("Status").IsRequired();
                entity.Property(e => e.Notes).HasColumnName("Notes").HasMaxLength(500);
                entity.Property(e => e.CreatorId).HasColumnName("CreatorId").IsRequired();
                entity.Property(e => e.CreatedAt).HasColumnName("CreatedAt").IsRequired();
                entity.Property(e => e.UpdatedAt).HasColumnName("UpdatedAt").IsRequired();

                // 索引
                entity.HasIndex(e => e.OrderCode).IsUnique().HasDatabaseName("IX_RepairOrders_OrderCode");
                entity.HasIndex(e => e.SupplierId).HasDatabaseName("IX_RepairOrders_SupplierId");
                entity.HasIndex(e => e.CreatorId).HasDatabaseName("IX_RepairOrders_CreatorId");

                // 关系
                entity.HasOne(e => e.Supplier)
                    .WithMany()
                    .HasForeignKey(e => e.SupplierId)
                    .OnDelete(DeleteBehavior.Restrict)
                    .HasConstraintName("FK_RepairOrders_Suppliers_SupplierId");

                entity.HasOne(e => e.Creator)
                    .WithMany()
                    .HasForeignKey(e => e.CreatorId)
                    .OnDelete(DeleteBehavior.Restrict)
                    .HasConstraintName("FK_RepairOrders_Users_CreatorId");
            });

            // --- RepairItem (INT PK) ---
            modelBuilder.Entity<RepairItem>(entity =>
            {
                entity.ToTable("RepairItems");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).HasColumnName("Id").ValueGeneratedOnAdd();

                entity.Property(e => e.RepairOrderId).HasColumnName("RepairOrderId").IsRequired();
                entity.Property(e => e.AssetId).HasColumnName("AssetId").IsRequired();
                entity.Property(e => e.FaultRecordId).HasColumnName("FaultRecordId");
                entity.Property(e => e.Description).HasColumnName("Description").HasMaxLength(500);
                entity.Property(e => e.RepairCost).HasColumnName("RepairCost").HasColumnType("decimal(18,2)");
                entity.Property(e => e.RepairStatus).HasColumnName("RepairStatus").IsRequired();
                entity.Property(e => e.RepairResult).HasColumnName("RepairResult").HasMaxLength(500);

                // 索引
                entity.HasIndex(e => e.RepairOrderId).HasDatabaseName("IX_RepairItems_RepairOrderId");
                entity.HasIndex(e => e.AssetId).HasDatabaseName("IX_RepairItems_AssetId");
                entity.HasIndex(e => e.FaultRecordId).HasDatabaseName("IX_RepairItems_FaultRecordId");

                // 关系
                entity.HasOne(e => e.RepairOrder)
                    .WithMany(ro => ro.RepairItems)
                    .HasForeignKey(e => e.RepairOrderId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_RepairItems_RepairOrders_RepairOrderId");

                entity.HasOne(e => e.Asset)
                    .WithMany()
                    .HasForeignKey(e => e.AssetId)
                    .OnDelete(DeleteBehavior.Restrict)
                    .HasConstraintName("FK_RepairItems_Assets_AssetId");

                entity.HasOne(e => e.FaultRecord)
                    .WithMany()
                    .HasForeignKey(e => e.FaultRecordId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("FK_RepairItems_FaultRecords_FaultRecordId");
            });

            // --- AssetSnapshot (BIGINT PK) ---
            modelBuilder.Entity<AssetSnapshot>(entity =>
            {
                entity.ToTable("AssetSnapshots");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).HasColumnName("Id").ValueGeneratedOnAdd();

                entity.Property(e => e.SnapshotDate).HasColumnName("SnapshotDate").IsRequired();
                entity.Property(e => e.AssetId).HasColumnName("AssetId").IsRequired();
                entity.Property(e => e.AssetCode).HasColumnName("AssetCode").IsRequired().HasMaxLength(30);
                entity.Property(e => e.FinancialCode).HasColumnName("FinancialCode").HasMaxLength(50);
                entity.Property(e => e.AssetName).HasColumnName("AssetName").IsRequired().HasMaxLength(100);
                entity.Property(e => e.AssetTypeId).HasColumnName("AssetTypeId").IsRequired();
                entity.Property(e => e.LocationId).HasColumnName("LocationId");
                entity.Property(e => e.DepartmentId).HasColumnName("DepartmentId");
                entity.Property(e => e.Status).HasColumnName("Status").IsRequired();
                entity.Property(e => e.Price).HasColumnName("Price").HasColumnType("decimal(18,2)");
                entity.Property(e => e.PurchaseDate).HasColumnName("PurchaseDate");

                // 索引
                entity.HasIndex(e => new { e.SnapshotDate, e.AssetId })
                    .IsUnique()
                    .HasDatabaseName("UK_AssetSnapshots_Date_AssetId");
            });
        }

        private void ConfigureNotificationModule(ModelBuilder modelBuilder)
        {
            // --- Notification (BIGINT PK) ---
            modelBuilder.Entity<Notification>(entity =>
            {
                entity.ToTable("notifications");
                entity.HasKey(e => e.NotificationId);

                // 修复字段映射 - 使用实际数据库表的字段名（大写驼峰命名）
                entity.Property(e => e.NotificationId).HasColumnName("NotificationId").ValueGeneratedOnAdd();
                entity.Property(e => e.UserId).HasColumnName("UserId").IsRequired(); // INT FK to Users
                entity.Property(e => e.Title).HasColumnName("Title").HasMaxLength(255).IsRequired();
                entity.Property(e => e.Content).HasColumnName("Content").HasColumnType("TEXT").IsRequired();
                entity.Property(e => e.Type).HasColumnName("Type").HasMaxLength(50).IsRequired();
                entity.Property(e => e.ReferenceType).HasColumnName("ReferenceType").HasMaxLength(50);
                entity.Property(e => e.ReferenceId).HasColumnName("ReferenceId");
                entity.Property(e => e.IsRead).HasColumnName("IsRead").HasDefaultValue(false);
                entity.Property(e => e.CreationTimestamp).HasColumnName("CreationTimestamp").IsRequired();
                entity.Property(e => e.ReadTimestamp).HasColumnName("ReadTimestamp");

                // 将不存在于数据库的属性标记为NotMapped
                entity.Ignore(e => e.Priority);
                entity.Ignore(e => e.ExtraData);
                entity.Ignore(e => e.TriggeredByUserId);

                // 这些属性也不在数据库中，但它们是为了API兼容性的NotMapped属性
                entity.Ignore(e => e.CreatedAt);
                entity.Ignore(e => e.ReadAt);
                entity.Ignore(e => e.ResourceType);
                entity.Ignore(e => e.ResourceId);

                // 修复关系映射 - 只保留基本的外键映射，不使用导航属性
                entity.HasOne<User>()
                      .WithMany()
                      .HasForeignKey(e => e.UserId)
                      .OnDelete(DeleteBehavior.Cascade)
                      .HasConstraintName("FK_Notifications_User");

                // 添加索引以提高查询性能（使用实际数据库中的索引名）
                entity.HasIndex(e => new { e.UserId, e.IsRead, e.CreationTimestamp })
                      .HasDatabaseName("idx_notifications_user_read_time");
            });
        }
        
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            base.OnConfiguring(optionsBuilder);
            // 在开发环境中启用敏感数据日志记录
            if (Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Development")
            {
            optionsBuilder.EnableSensitiveDataLogging();
            }
        }
    }
}
