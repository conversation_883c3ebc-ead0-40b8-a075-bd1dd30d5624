// File: Infrastructure/Services/PeriodicTaskGenerationService.cs
// Description: 周期性任务生成服务，负责定期检查和生成新的任务实例

using System;
using System.Threading;
using System.Threading.Tasks;
using System.Collections.Generic;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Cronos;
using ItAssetsSystem.Core.Abstractions;
using ItAssetsSystem.Domain.Entities.Tasks;
using System.Linq;
using TaskEntity = ItAssetsSystem.Domain.Entities.Tasks.Task;
using ItAssetsSystem.Application.Features.Tasks.Dtos;
using ItAssetsSystem.Core.Events;
using ItAssetsSystem.Core.Events.Tasks;

namespace ItAssetsSystem.Infrastructure.Services
{
    /// <summary>
    /// 周期性任务生成服务
    /// </summary>
    public class PeriodicTaskGenerationService : BackgroundService
    {
        private readonly ILogger<PeriodicTaskGenerationService> _logger;
        private readonly IServiceProvider _serviceProvider;
        private readonly IEventBus _eventBus;
        private readonly TimeSpan _checkInterval = TimeSpan.FromMinutes(1); // 每分钟检查一次

        public PeriodicTaskGenerationService(
            ILogger<PeriodicTaskGenerationService> logger,
            IServiceProvider serviceProvider,
            IEventBus eventBus)
        {
            _logger = logger;
            _serviceProvider = serviceProvider;
            _eventBus = eventBus;
        }

        protected override async System.Threading.Tasks.Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("周期性任务生成服务已启动");

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await ProcessPeriodicTasksAsync(stoppingToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "处理周期性任务的顶层循环中发生错误");
                }

                try
                {
                    await System.Threading.Tasks.Task.Delay(_checkInterval, stoppingToken);
                }
                catch (TaskCanceledException)
                {
                    // 正常的取消操作，不需要记录错误
                    break;
                }
            }
            _logger.LogInformation("周期性任务生成服务已停止");
        }

        private async System.Threading.Tasks.Task ProcessPeriodicTasksAsync(CancellationToken stoppingToken)
        {
            using var scope = _serviceProvider.CreateScope();
            var taskRepository = scope.ServiceProvider.GetRequiredService<ITaskRepository>();
            var now = DateTime.Now;

            var allEnabledSchedules = await taskRepository.GetAllPeriodicSchedulesAsync(false);
            
            _logger.LogInformation("检查 {Count} 个启用的周期性任务计划。", allEnabledSchedules.Count);

            foreach (var schedule in allEnabledSchedules.Where(s => s.NextGenerationTime <= now))
            {
                if (stoppingToken.IsCancellationRequested)
                {
                    _logger.LogInformation("周期性任务处理被中断。");
                    break;
                }

                try
                {
                    _logger.LogInformation("处理周期性任务计划 ID: {ScheduleId}, 名称: {ScheduleName}", schedule.PeriodicTaskScheduleId, schedule.Name);
                    var templateTask = await taskRepository.GetTaskByIdAsync(schedule.TemplateTaskId);
                    if (templateTask == null)
                    {
                        _logger.LogError("找不到周期性任务计划 {ScheduleId} (名称: {ScheduleName}) 的模板任务 {TemplateTaskId}",
                            schedule.PeriodicTaskScheduleId, schedule.Name, schedule.TemplateTaskId);
                        continue;
                    }

                    // 获取负责人列表，从新的负责人关联表中获取
                    var assigneeUserIds = new List<int>();

                    // 从 periodic_task_schedule_assignees 表中获取负责人
                    var assignees = await taskRepository.GetPeriodicScheduleAssigneesAsync(schedule.PeriodicTaskScheduleId);
                    if (assignees != null && assignees.Count > 0)
                    {
                        assigneeUserIds.AddRange(assignees.Select(a => a.UserId));
                        _logger.LogInformation("从数据库获取到 {Count} 个负责人: {Ids}", assignees.Count, string.Join(",", assigneeUserIds));
                    }

                    // 如果没有从数据库中获取到负责人，使用模板任务的负责人
                    if (assigneeUserIds.Count == 0 && templateTask.AssigneeUserId.HasValue)
                    {
                        assigneeUserIds.Add(templateTask.AssigneeUserId.Value);
                        _logger.LogInformation("使用模板任务的负责人: {UserId}", templateTask.AssigneeUserId.Value);
                    }

                    // 如果仍然没有负责人，使用创建者作为默认负责人
                    if (assigneeUserIds.Count == 0)
                    {
                        assigneeUserIds.Add(schedule.CreatorUserId);
                        _logger.LogInformation("使用创建者作为默认负责人: {UserId}", schedule.CreatorUserId);
                    }

                    // 创建一个任务，但有多个负责人
                    var newTask = new TaskEntity
                    {
                        Name = templateTask.Name,
                        Description = templateTask.Description,
                        TaskType = Models.Enums.TaskType.Periodic.ToString(),
                        Priority = templateTask.Priority, // 使用模板任务的优先级
                        Status = "Pending",
                        CreatorUserId = schedule.CreatorUserId,
                        AssigneeUserId = assigneeUserIds.Count > 0 ? assigneeUserIds[0] : schedule.CreatorUserId, // 主负责人设为第一个
                        LocationId = templateTask.LocationId, // 使用模板任务的位置
                        AssetId = templateTask.AssetId, // 使用模板任务的资产
                        PlanStartDate = now,
                        PlanEndDate = CalculateTaskDueDate(now, templateTask.PlanEndDate, templateTask.PlanStartDate),
                        CreationTimestamp = now,
                        LastUpdatedTimestamp = now,
                        PeriodicTaskScheduleId = schedule.PeriodicTaskScheduleId,
                        Points = templateTask.Points,
                        ProjectId = templateTask.ProjectId
                    };

                    var createdTask = await taskRepository.AddTaskAsync(newTask);
                    _logger.LogInformation("为计划 {ScheduleId} 创建了新任务 ID: {NewTaskId}，共有 {AssigneeCount} 个负责人",
                        schedule.PeriodicTaskScheduleId, createdTask.TaskId, assigneeUserIds.Count);

                    // 为所有负责人在 taskassignees 表中创建记录（与普通任务创建逻辑保持一致）
                    for (int i = 0; i < assigneeUserIds.Count; i++)
                    {
                        var assigneeUserId = assigneeUserIds[i];

                        try
                        {
                            await taskRepository.AddAssigneeAsync(new Domain.Entities.Tasks.TaskAssignee
                            {
                                TaskId = createdTask.TaskId,
                                UserId = assigneeUserId,
                                AssignedByUserId = schedule.CreatorUserId,
                                AssignmentType = i == 0 ? "Assignee" : "Participant", // 第一个为主负责人，其他为协作者
                                AssignmentTimestamp = DateTime.Now
                            });
                            _logger.LogInformation("已添加{Role} {UserId} 到任务 {TaskId} 的 taskassignees 表",
                                i == 0 ? "主负责人" : "协作者", assigneeUserId, createdTask.TaskId);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "添加{Role} {UserId} 到任务 {TaskId} 的 taskassignees 表失败",
                                i == 0 ? "主负责人" : "协作者", assigneeUserId, createdTask.TaskId);
                        }
                    }

                    // 发布任务创建事件
                    var taskCreatedEvent = new TaskCreatedEvent(
                        createdTask.TaskId,
                        createdTask.Name,
                        createdTask.CreatorUserId,
                        createdTask.AssigneeUserId,
                        createdTask.TaskType,
                        createdTask.PlanEndDate,
                        createdTask.Points,
                        createdTask.CreationTimestamp
                    );
                    _eventBus.Publish(taskCreatedEvent);

                    var createdTasks = new List<TaskEntity> { createdTask };

                    var cronExpression = CronExpression.Parse(schedule.CronExpression, CronFormat.Standard);
                    // 使用本地时间计算下次执行时间
                    var nextGenerationTimeUtc = cronExpression.GetNextOccurrence(DateTime.UtcNow, TimeZoneInfo.Local);
                    var nextGenerationTime = nextGenerationTimeUtc?.ToLocalTime();

                    // 更新生成统计信息
                    await taskRepository.UpdateGenerationStatisticsAsync(
                        schedule.PeriodicTaskScheduleId,
                        createdTasks.Count,
                        nextGenerationTime);

                    var taskIds = string.Join(", ", createdTasks.Select(t => t.TaskId));
                    if (nextGenerationTime.HasValue)
                    {
                        _logger.LogInformation(
                            "已为周期性任务计划 {ScheduleId} (名称: {ScheduleName}) 生成 {TaskCount} 个新任务实例，ID: {TaskIds}。已生成总数: {TotalGenerated}，下次生成时间：{NextGenerationTime}",
                            schedule.PeriodicTaskScheduleId, schedule.Name, createdTasks.Count, taskIds, schedule.OccurrencesGenerated + createdTasks.Count, nextGenerationTime.Value);
                    }
                    else
                    {
                        _logger.LogInformation(
                            "已为周期性任务计划 {ScheduleId} (名称: {ScheduleName}) 生成 {TaskCount} 个新任务实例，ID: {TaskIds}。已生成总数: {TotalGenerated}",
                            schedule.PeriodicTaskScheduleId, schedule.Name, createdTasks.Count, taskIds, schedule.OccurrencesGenerated + createdTasks.Count);
                        _logger.LogWarning("无法计算周期性任务计划 {ScheduleId} (名称: {ScheduleName}) 的下次生成时间，Cron表达式：{CronExpression}。可能计划已完成或配置错误。",
                            schedule.PeriodicTaskScheduleId, schedule.Name, schedule.CronExpression);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex,
                        "处理周期性任务计划 {ScheduleId} (名称: {ScheduleName}) 时发生错误",
                        schedule.PeriodicTaskScheduleId, schedule.Name);
                }
            }
        }

        private DateTime? CalculateTaskDueDate(DateTime now, DateTime? templateDueDate, DateTime? templateStartDate)
        {
            if (!templateDueDate.HasValue || !templateStartDate.HasValue || templateStartDate.Value >= templateDueDate.Value)
            {
                return now.AddDays(7);
            }

            var duration = templateDueDate.Value - templateStartDate.Value;
            return now.Add(duration);
        }


    }
} 