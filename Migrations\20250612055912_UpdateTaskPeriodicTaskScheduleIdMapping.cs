﻿using System;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ItAssetsSystem.Migrations
{
    public partial class UpdateTaskPeriodicTaskScheduleIdMapping : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AssetReceives_PurchaseOrders_PurchaseOrderId",
                table: "AssetReceives");

            migrationBuilder.DropForeignKey(
                name: "FK_AssetReceives_ReturnToFactories_ReturnToFactoryId",
                table: "AssetReceives");

            migrationBuilder.DropForeignKey(
                name: "FK_attachments_comments_CommentId",
                table: "attachments");

            migrationBuilder.DropForeignKey(
                name: "FK_attachments_tasks_TaskId",
                table: "attachments");

            migrationBuilder.DropForeignKey(
                name: "FK_attachments_users_UploaderUserId",
                table: "attachments");

            migrationBuilder.DropForeignKey(
                name: "FK_comments_comments_ParentCommentId",
                table: "comments");

            migrationBuilder.DropForeignKey(
                name: "FK_comments_tasks_TaskId",
                table: "comments");

            migrationBuilder.DropForeignKey(
                name: "FK_comments_users_UserId",
                table: "comments");

            migrationBuilder.DropForeignKey(
                name: "FK_FaultRecords_assets_AssetId",
                table: "FaultRecords");

            migrationBuilder.DropForeignKey(
                name: "FK_FaultRecords_FaultTypes_FaultTypeId",
                table: "FaultRecords");

            migrationBuilder.DropForeignKey(
                name: "FK_FaultRecords_locations_LocationId",
                table: "FaultRecords");

            migrationBuilder.DropForeignKey(
                name: "FK_FaultRecords_users_AssigneeId",
                table: "FaultRecords");

            migrationBuilder.DropForeignKey(
                name: "FK_FaultRecords_users_ReporterId",
                table: "FaultRecords");

            migrationBuilder.DropForeignKey(
                name: "FK_MaintenanceOrders_FaultRecords_FaultRecordId",
                table: "MaintenanceOrders");

            migrationBuilder.DropForeignKey(
                name: "FK_pdcaplans_tasks_TaskId",
                table: "pdcaplans");

            migrationBuilder.DropForeignKey(
                name: "FK_pdcaplans_users_creator_user_id",
                table: "pdcaplans");

            migrationBuilder.DropForeignKey(
                name: "FK_pdcaplans_users_responsible_person_id",
                table: "pdcaplans");

            migrationBuilder.DropForeignKey(
                name: "FK_periodictaskschedules_tasks_template_task_id",
                table: "periodictaskschedules");

            migrationBuilder.DropForeignKey(
                name: "FK_periodictaskschedules_users_creator_user_id",
                table: "periodictaskschedules");

            migrationBuilder.DropForeignKey(
                name: "FK_PurchaseItems_assettypes_AssetTypeId",
                table: "PurchaseItems");

            migrationBuilder.DropForeignKey(
                name: "FK_PurchaseItems_PurchaseOrders_PurchaseOrderId",
                table: "PurchaseItems");

            migrationBuilder.DropForeignKey(
                name: "FK_PurchaseOrders_Suppliers_SupplierId",
                table: "PurchaseOrders");

            migrationBuilder.DropForeignKey(
                name: "FK_PurchaseOrders_users_RequesterId",
                table: "PurchaseOrders");

            migrationBuilder.DropForeignKey(
                name: "FK_quick_memos_quick_memo_categories_category_id",
                table: "quick_memos");

            migrationBuilder.DropForeignKey(
                name: "FK_ReturnToFactories_assets_AssetId",
                table: "ReturnToFactories");

            migrationBuilder.DropForeignKey(
                name: "FK_ReturnToFactories_Suppliers_SupplierId",
                table: "ReturnToFactories");

            migrationBuilder.DropForeignKey(
                name: "FK_ReturnToFactories_users_OperatorId",
                table: "ReturnToFactories");

            migrationBuilder.DropForeignKey(
                name: "FK_spare_part_transactions_spare_part_locations_location_id",
                table: "spare_part_transactions");

            migrationBuilder.DropForeignKey(
                name: "FK_spare_part_transactions_spare_parts_part_id",
                table: "spare_part_transactions");

            migrationBuilder.DropForeignKey(
                name: "FK_spare_part_types_spare_part_types_parent_id",
                table: "spare_part_types");

            migrationBuilder.DropForeignKey(
                name: "FK_spare_parts_spare_part_locations_location_id",
                table: "spare_parts");

            migrationBuilder.DropForeignKey(
                name: "FK_spare_parts_spare_part_types_type_id",
                table: "spare_parts");

            migrationBuilder.DropForeignKey(
                name: "FK_taskassignees_tasks_TaskId",
                table: "taskassignees");

            migrationBuilder.DropForeignKey(
                name: "FK_taskassignees_users_AssignedByUserId",
                table: "taskassignees");

            migrationBuilder.DropForeignKey(
                name: "FK_taskassignees_users_UserId",
                table: "taskassignees");

            migrationBuilder.DropForeignKey(
                name: "FK_taskhistory_attachments_AttachmentId",
                table: "taskhistory");

            migrationBuilder.DropForeignKey(
                name: "FK_taskhistory_comments_CommentId",
                table: "taskhistory");

            migrationBuilder.DropForeignKey(
                name: "FK_taskhistory_tasks_TaskId",
                table: "taskhistory");

            migrationBuilder.DropForeignKey(
                name: "FK_taskhistory_users_UserId",
                table: "taskhistory");

            migrationBuilder.DropForeignKey(
                name: "FK_tasks_assets_AssetId",
                table: "tasks");

            migrationBuilder.DropForeignKey(
                name: "FK_tasks_locations_LocationId",
                table: "tasks");

            migrationBuilder.DropForeignKey(
                name: "FK_tasks_tasks_ParentTaskId",
                table: "tasks");

            migrationBuilder.DropForeignKey(
                name: "FK_tasks_tasks_PreviousInstanceTaskId",
                table: "tasks");

            migrationBuilder.DropForeignKey(
                name: "FK_tasks_users_AssigneeUserId",
                table: "tasks");

            migrationBuilder.DropForeignKey(
                name: "FK_tasks_users_CreatorUserId",
                table: "tasks");

            migrationBuilder.DropPrimaryKey(
                name: "PK_tasks",
                table: "tasks");

            migrationBuilder.DropIndex(
                name: "IX_tasks_PreviousInstanceTaskId",
                table: "tasks");

            migrationBuilder.DropPrimaryKey(
                name: "PK_taskassignees",
                table: "taskassignees");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Suppliers",
                table: "Suppliers");

            migrationBuilder.DropIndex(
                name: "IX_spare_parts_code",
                table: "spare_parts");

            migrationBuilder.DropIndex(
                name: "IX_spare_parts_quantity",
                table: "spare_parts");

            migrationBuilder.DropIndex(
                name: "IX_spare_part_types_code",
                table: "spare_part_types");

            migrationBuilder.DropIndex(
                name: "IX_spare_part_types_path",
                table: "spare_part_types");

            migrationBuilder.DropIndex(
                name: "IX_spare_part_transactions_batch_number",
                table: "spare_part_transactions");

            migrationBuilder.DropIndex(
                name: "IX_spare_part_transactions_transaction_time",
                table: "spare_part_transactions");

            migrationBuilder.DropIndex(
                name: "IX_spare_part_transactions_type",
                table: "spare_part_transactions");

            migrationBuilder.DropIndex(
                name: "IX_spare_part_locations_area",
                table: "spare_part_locations");

            migrationBuilder.DropIndex(
                name: "IX_spare_part_locations_code",
                table: "spare_part_locations");

            migrationBuilder.DropPrimaryKey(
                name: "PK_ReturnToFactories",
                table: "ReturnToFactories");

            migrationBuilder.DropIndex(
                name: "IX_ReturnToFactories_OperatorId",
                table: "ReturnToFactories");

            migrationBuilder.DropIndex(
                name: "ix_quick_memos_user_category",
                table: "quick_memos");

            migrationBuilder.DropIndex(
                name: "ix_quick_memos_user_pinned_updated",
                table: "quick_memos");

            migrationBuilder.DropIndex(
                name: "ix_quick_memo_categories_user_name",
                table: "quick_memo_categories");

            migrationBuilder.DropPrimaryKey(
                name: "PK_PurchaseOrders",
                table: "PurchaseOrders");

            migrationBuilder.DropPrimaryKey(
                name: "PK_PurchaseItems",
                table: "PurchaseItems");

            migrationBuilder.DropPrimaryKey(
                name: "PK_periodictaskschedules",
                table: "periodictaskschedules");

            migrationBuilder.DropIndex(
                name: "IX_periodictaskschedules_template_task_id",
                table: "periodictaskschedules");

            migrationBuilder.DropPrimaryKey(
                name: "PK_pdcaplans",
                table: "pdcaplans");

            migrationBuilder.DropIndex(
                name: "IX_pdcaplans_TaskId",
                table: "pdcaplans");

            migrationBuilder.DropPrimaryKey(
                name: "PK_FaultRecords",
                table: "FaultRecords");

            migrationBuilder.DropPrimaryKey(
                name: "PK_comments",
                table: "comments");

            migrationBuilder.DropPrimaryKey(
                name: "PK_attachments",
                table: "attachments");

            migrationBuilder.DropPrimaryKey(
                name: "PK_taskhistory",
                table: "taskhistory");

            migrationBuilder.DropColumn(
                name: "CreatedBy",
                table: "ReturnToFactories");

            migrationBuilder.DropColumn(
                name: "Reason",
                table: "ReturnToFactories");

            migrationBuilder.DropColumn(
                name: "ReturnDate",
                table: "ReturnToFactories");

            migrationBuilder.DropColumn(
                name: "UpdatedBy",
                table: "ReturnToFactories");

            migrationBuilder.DropColumn(
                name: "IsAssetGenerated",
                table: "PurchaseItems");

            migrationBuilder.RenameTable(
                name: "tasks",
                newName: "Tasks");

            migrationBuilder.RenameTable(
                name: "taskassignees",
                newName: "TaskAssignees");

            migrationBuilder.RenameTable(
                name: "Suppliers",
                newName: "suppliers");

            migrationBuilder.RenameTable(
                name: "ReturnToFactories",
                newName: "returntofactories");

            migrationBuilder.RenameTable(
                name: "PurchaseOrders",
                newName: "purchaseorders");

            migrationBuilder.RenameTable(
                name: "PurchaseItems",
                newName: "purchaseitems");

            migrationBuilder.RenameTable(
                name: "periodictaskschedules",
                newName: "PeriodicTaskSchedules");

            migrationBuilder.RenameTable(
                name: "pdcaplans",
                newName: "PdcaPlans");

            migrationBuilder.RenameTable(
                name: "FaultRecords",
                newName: "faultrecords");

            migrationBuilder.RenameTable(
                name: "comments",
                newName: "Comments");

            migrationBuilder.RenameTable(
                name: "attachments",
                newName: "Attachments");

            migrationBuilder.RenameTable(
                name: "taskhistory",
                newName: "TaskHistories");

            migrationBuilder.RenameIndex(
                name: "IX_tasks_ParentTaskId",
                table: "Tasks",
                newName: "IX_Tasks_ParentTaskId");

            migrationBuilder.RenameIndex(
                name: "IX_tasks_LocationId",
                table: "Tasks",
                newName: "IX_Tasks_LocationId");

            migrationBuilder.RenameIndex(
                name: "IX_tasks_CreatorUserId",
                table: "Tasks",
                newName: "IX_Tasks_CreatorUserId");

            migrationBuilder.RenameIndex(
                name: "IX_tasks_AssigneeUserId",
                table: "Tasks",
                newName: "IX_Tasks_AssigneeUserId");

            migrationBuilder.RenameIndex(
                name: "IX_tasks_AssetId",
                table: "Tasks",
                newName: "IX_Tasks_AssetId");

            migrationBuilder.RenameIndex(
                name: "IX_taskassignees_UserId",
                table: "TaskAssignees",
                newName: "IX_TaskAssignees_UserId");

            migrationBuilder.RenameIndex(
                name: "IX_taskassignees_TaskId",
                table: "TaskAssignees",
                newName: "IX_TaskAssignees_TaskId");

            migrationBuilder.RenameIndex(
                name: "IX_taskassignees_AssignedByUserId",
                table: "TaskAssignees",
                newName: "IX_TaskAssignees_AssignedByUserId");

            migrationBuilder.RenameColumn(
                name: "Result",
                table: "returntofactories",
                newName: "RepairResult");

            migrationBuilder.RenameColumn(
                name: "Remarks",
                table: "returntofactories",
                newName: "Notes");

            migrationBuilder.RenameColumn(
                name: "OperatorId",
                table: "returntofactories",
                newName: "SenderId");

            migrationBuilder.RenameColumn(
                name: "ExpectedReturnDate",
                table: "returntofactories",
                newName: "SendTime");

            migrationBuilder.RenameColumn(
                name: "ActualReturnDate",
                table: "returntofactories",
                newName: "EstimatedReturnTime");

            migrationBuilder.RenameIndex(
                name: "IX_ReturnToFactories_SupplierId",
                table: "returntofactories",
                newName: "IX_returntofactories_SupplierId");

            migrationBuilder.RenameIndex(
                name: "IX_ReturnToFactories_AssetId",
                table: "returntofactories",
                newName: "IX_returntofactories_AssetId");

            migrationBuilder.RenameIndex(
                name: "ix_quick_memos_user_id",
                table: "quick_memos",
                newName: "IX_quick_memos_user_id");

            migrationBuilder.RenameColumn(
                name: "RequesterId",
                table: "purchaseorders",
                newName: "ApplicantId");

            migrationBuilder.RenameColumn(
                name: "OrderNumber",
                table: "purchaseorders",
                newName: "OrderCode");

            migrationBuilder.RenameColumn(
                name: "ExpectedDeliveryDate",
                table: "purchaseorders",
                newName: "EstimatedDeliveryDate");

            migrationBuilder.RenameIndex(
                name: "IX_PurchaseOrders_SupplierId",
                table: "purchaseorders",
                newName: "IX_purchaseorders_SupplierId");

            migrationBuilder.RenameIndex(
                name: "IX_PurchaseOrders_RequesterId",
                table: "purchaseorders",
                newName: "IX_purchaseorders_ApplicantId");

            migrationBuilder.RenameColumn(
                name: "Name",
                table: "purchaseitems",
                newName: "ItemName");

            migrationBuilder.RenameIndex(
                name: "IX_PurchaseItems_PurchaseOrderId",
                table: "purchaseitems",
                newName: "IX_purchaseitems_PurchaseOrderId");

            migrationBuilder.RenameIndex(
                name: "IX_PurchaseItems_AssetTypeId",
                table: "purchaseitems",
                newName: "IX_purchaseitems_AssetTypeId");

            migrationBuilder.RenameColumn(
                name: "status",
                table: "PeriodicTaskSchedules",
                newName: "Status");

            migrationBuilder.RenameColumn(
                name: "name",
                table: "PeriodicTaskSchedules",
                newName: "Name");

            migrationBuilder.RenameColumn(
                name: "description",
                table: "PeriodicTaskSchedules",
                newName: "Description");

            migrationBuilder.RenameColumn(
                name: "week_of_month",
                table: "PeriodicTaskSchedules",
                newName: "WeekOfMonth");

            migrationBuilder.RenameColumn(
                name: "total_occurrences",
                table: "PeriodicTaskSchedules",
                newName: "TotalOccurrences");

            migrationBuilder.RenameColumn(
                name: "template_task_id",
                table: "PeriodicTaskSchedules",
                newName: "TemplateTaskId");

            migrationBuilder.RenameColumn(
                name: "start_date",
                table: "PeriodicTaskSchedules",
                newName: "StartDate");

            migrationBuilder.RenameColumn(
                name: "recurrence_type",
                table: "PeriodicTaskSchedules",
                newName: "RecurrenceType");

            migrationBuilder.RenameColumn(
                name: "recurrence_interval",
                table: "PeriodicTaskSchedules",
                newName: "RecurrenceInterval");

            migrationBuilder.RenameColumn(
                name: "occurrences_generated",
                table: "PeriodicTaskSchedules",
                newName: "OccurrencesGenerated");

            migrationBuilder.RenameColumn(
                name: "next_generation_time",
                table: "PeriodicTaskSchedules",
                newName: "NextGenerationTime");

            migrationBuilder.RenameColumn(
                name: "month_of_year",
                table: "PeriodicTaskSchedules",
                newName: "MonthOfYear");

            migrationBuilder.RenameColumn(
                name: "last_updated_timestamp",
                table: "PeriodicTaskSchedules",
                newName: "LastUpdatedTimestamp");

            migrationBuilder.RenameColumn(
                name: "last_generated_timestamp",
                table: "PeriodicTaskSchedules",
                newName: "LastGeneratedTimestamp");

            migrationBuilder.RenameColumn(
                name: "last_error",
                table: "PeriodicTaskSchedules",
                newName: "LastError");

            migrationBuilder.RenameColumn(
                name: "end_date",
                table: "PeriodicTaskSchedules",
                newName: "EndDate");

            migrationBuilder.RenameColumn(
                name: "end_condition_type",
                table: "PeriodicTaskSchedules",
                newName: "EndConditionType");

            migrationBuilder.RenameColumn(
                name: "default_points",
                table: "PeriodicTaskSchedules",
                newName: "DefaultPoints");

            migrationBuilder.RenameColumn(
                name: "days_of_week",
                table: "PeriodicTaskSchedules",
                newName: "DaysOfWeek");

            migrationBuilder.RenameColumn(
                name: "day_of_week_for_month",
                table: "PeriodicTaskSchedules",
                newName: "DayOfWeekForMonth");

            migrationBuilder.RenameColumn(
                name: "day_of_month",
                table: "PeriodicTaskSchedules",
                newName: "DayOfMonth");

            migrationBuilder.RenameColumn(
                name: "cron_expression",
                table: "PeriodicTaskSchedules",
                newName: "CronExpression");

            migrationBuilder.RenameColumn(
                name: "creator_user_id",
                table: "PeriodicTaskSchedules",
                newName: "CreatorUserId");

            migrationBuilder.RenameColumn(
                name: "creation_timestamp",
                table: "PeriodicTaskSchedules",
                newName: "CreationTimestamp");

            migrationBuilder.RenameColumn(
                name: "periodic_task_schedule_id",
                table: "PeriodicTaskSchedules",
                newName: "PeriodicTaskScheduleId");

            migrationBuilder.RenameIndex(
                name: "IX_periodictaskschedules_creator_user_id",
                table: "PeriodicTaskSchedules",
                newName: "IX_PeriodicTaskSchedules_CreatorUserId");

            migrationBuilder.RenameColumn(
                name: "title",
                table: "PdcaPlans",
                newName: "Title");

            migrationBuilder.RenameColumn(
                name: "status",
                table: "PdcaPlans",
                newName: "Status");

            migrationBuilder.RenameColumn(
                name: "stage",
                table: "PdcaPlans",
                newName: "Stage");

            migrationBuilder.RenameColumn(
                name: "notes",
                table: "PdcaPlans",
                newName: "Notes");

            migrationBuilder.RenameColumn(
                name: "goal",
                table: "PdcaPlans",
                newName: "Goal");

            migrationBuilder.RenameColumn(
                name: "responsible_person_id",
                table: "PdcaPlans",
                newName: "ResponsiblePersonId");

            migrationBuilder.RenameColumn(
                name: "plan_content",
                table: "PdcaPlans",
                newName: "PlanContent");

            migrationBuilder.RenameColumn(
                name: "last_updated_timestamp",
                table: "PdcaPlans",
                newName: "LastUpdatedTimestamp");

            migrationBuilder.RenameColumn(
                name: "do_record",
                table: "PdcaPlans",
                newName: "DoRecord");

            migrationBuilder.RenameColumn(
                name: "creator_user_id",
                table: "PdcaPlans",
                newName: "CreatorUserId");

            migrationBuilder.RenameColumn(
                name: "creation_timestamp",
                table: "PdcaPlans",
                newName: "CreationTimestamp");

            migrationBuilder.RenameColumn(
                name: "completion_rate",
                table: "PdcaPlans",
                newName: "CompletionRate");

            migrationBuilder.RenameColumn(
                name: "check_result",
                table: "PdcaPlans",
                newName: "CheckResult");

            migrationBuilder.RenameColumn(
                name: "act_action",
                table: "PdcaPlans",
                newName: "ActAction");

            migrationBuilder.RenameColumn(
                name: "pdca_plan_id",
                table: "PdcaPlans",
                newName: "PdcaPlanId");

            migrationBuilder.RenameIndex(
                name: "IX_pdcaplans_responsible_person_id",
                table: "PdcaPlans",
                newName: "IX_PdcaPlans_ResponsiblePersonId");

            migrationBuilder.RenameIndex(
                name: "IX_pdcaplans_creator_user_id",
                table: "PdcaPlans",
                newName: "IX_PdcaPlans_CreatorUserId");

            migrationBuilder.RenameIndex(
                name: "IX_FaultRecords_ReporterId",
                table: "faultrecords",
                newName: "IX_faultrecords_ReporterId");

            migrationBuilder.RenameIndex(
                name: "IX_FaultRecords_LocationId",
                table: "faultrecords",
                newName: "IX_faultrecords_LocationId");

            migrationBuilder.RenameIndex(
                name: "IX_FaultRecords_FaultTypeId",
                table: "faultrecords",
                newName: "IX_faultrecords_FaultTypeId");

            migrationBuilder.RenameIndex(
                name: "IX_FaultRecords_AssigneeId",
                table: "faultrecords",
                newName: "IX_faultrecords_AssigneeId");

            migrationBuilder.RenameIndex(
                name: "IX_FaultRecords_AssetId",
                table: "faultrecords",
                newName: "IX_faultrecords_AssetId");

            migrationBuilder.RenameIndex(
                name: "IX_comments_UserId",
                table: "Comments",
                newName: "IX_Comments_UserId");

            migrationBuilder.RenameIndex(
                name: "IX_comments_TaskId",
                table: "Comments",
                newName: "IX_Comments_TaskId");

            migrationBuilder.RenameIndex(
                name: "IX_comments_ParentCommentId",
                table: "Comments",
                newName: "IX_Comments_ParentCommentId");

            migrationBuilder.RenameIndex(
                name: "IX_attachments_UploaderUserId",
                table: "Attachments",
                newName: "IX_Attachments_UploaderUserId");

            migrationBuilder.RenameIndex(
                name: "IX_attachments_TaskId",
                table: "Attachments",
                newName: "IX_Attachments_TaskId");

            migrationBuilder.RenameIndex(
                name: "IX_attachments_CommentId",
                table: "Attachments",
                newName: "IX_Attachments_CommentId");

            migrationBuilder.RenameIndex(
                name: "IX_taskhistory_UserId",
                table: "TaskHistories",
                newName: "IX_TaskHistories_UserId");

            migrationBuilder.RenameIndex(
                name: "IX_taskhistory_TaskId",
                table: "TaskHistories",
                newName: "IX_TaskHistories_TaskId");

            migrationBuilder.RenameIndex(
                name: "IX_taskhistory_CommentId",
                table: "TaskHistories",
                newName: "IX_TaskHistories_CommentId");

            migrationBuilder.RenameIndex(
                name: "IX_taskhistory_AttachmentId",
                table: "TaskHistories",
                newName: "IX_TaskHistories_AttachmentId");

            migrationBuilder.AddColumn<string>(
                name: "material_number",
                table: "spare_parts",
                type: "varchar(50)",
                maxLength: 50,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<DateTime>(
                name: "ActualReturnTime",
                table: "returntofactories",
                type: "datetime(6)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Code",
                table: "returntofactories",
                type: "varchar(50)",
                maxLength: 50,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<int>(
                name: "FaultRecordId",
                table: "returntofactories",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<bool>(
                name: "InWarranty",
                table: "returntofactories",
                type: "tinyint(1)",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<decimal>(
                name: "RepairCost",
                table: "returntofactories",
                type: "decimal(65,30)",
                nullable: true);

            migrationBuilder.AlterColumn<bool>(
                name: "is_pinned",
                table: "quick_memos",
                type: "tinyint(1)",
                nullable: false,
                oldClrType: typeof(bool),
                oldType: "tinyint(1)",
                oldDefaultValue: false);

            migrationBuilder.AddColumn<DateTime>(
                name: "ApplicationTime",
                table: "purchaseorders",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "ApprovalTime",
                table: "purchaseorders",
                type: "datetime(6)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ApproverId",
                table: "purchaseorders",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Description",
                table: "purchaseorders",
                type: "longtext",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "Title",
                table: "purchaseorders",
                type: "longtext",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<int>(
                name: "AssetTypeId",
                table: "purchaseitems",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AddColumn<string>(
                name: "ItemCode",
                table: "purchaseitems",
                type: "longtext",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<long>(
                name: "TemplateTaskTaskId",
                table: "PeriodicTaskSchedules",
                type: "bigint",
                nullable: false,
                defaultValue: 0L);

            migrationBuilder.AlterColumn<int>(
                name: "AssetId",
                table: "faultrecords",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Tasks",
                table: "Tasks",
                column: "TaskId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_TaskAssignees",
                table: "TaskAssignees",
                column: "TaskAssigneeId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_suppliers",
                table: "suppliers",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_returntofactories",
                table: "returntofactories",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_purchaseorders",
                table: "purchaseorders",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_purchaseitems",
                table: "purchaseitems",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_PeriodicTaskSchedules",
                table: "PeriodicTaskSchedules",
                column: "PeriodicTaskScheduleId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_PdcaPlans",
                table: "PdcaPlans",
                column: "PdcaPlanId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_faultrecords",
                table: "faultrecords",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Comments",
                table: "Comments",
                column: "CommentId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Attachments",
                table: "Attachments",
                column: "AttachmentId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_TaskHistories",
                table: "TaskHistories",
                column: "TaskHistoryId");

            migrationBuilder.CreateTable(
                name: "AssetSnapshots",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    SnapshotDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    AssetId = table.Column<int>(type: "int", nullable: false),
                    AssetCode = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    FinancialCode = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    AssetName = table.Column<string>(type: "varchar(200)", maxLength: 200, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    AssetTypeId = table.Column<int>(type: "int", nullable: false),
                    LocationId = table.Column<int>(type: "int", nullable: true),
                    DepartmentId = table.Column<int>(type: "int", nullable: true),
                    Status = table.Column<int>(type: "int", nullable: false),
                    Price = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    PurchaseDate = table.Column<DateTime>(type: "datetime(6)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AssetSnapshots", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "notifications",
                columns: table => new
                {
                    NotificationId = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    UserId = table.Column<int>(type: "int", nullable: false),
                    Title = table.Column<string>(type: "varchar(255)", maxLength: 255, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Content = table.Column<string>(type: "longtext", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Type = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ReferenceId = table.Column<long>(type: "bigint", nullable: true),
                    ReferenceType = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    IsRead = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    CreationTimestamp = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ReadTimestamp = table.Column<DateTime>(type: "datetime(6)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_notifications", x => x.NotificationId);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "periodic_task_schedule_assignees",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    periodic_task_schedule_id = table.Column<long>(type: "bigint", nullable: false),
                    user_id = table.Column<int>(type: "int", nullable: false),
                    created_at = table.Column<DateTime>(type: "datetime(6)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_periodic_task_schedule_assignees", x => x.id);
                    table.ForeignKey(
                        name: "FK_periodic_task_schedule_assignees_PeriodicTaskSchedules_perio~",
                        column: x => x.periodic_task_schedule_id,
                        principalTable: "PeriodicTaskSchedules",
                        principalColumn: "PeriodicTaskScheduleId",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "RepairOrders",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    OrderCode = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    SupplierId = table.Column<int>(type: "int", nullable: false),
                    SendDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    ExpectedReturnDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    ActualReturnDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    TotalCost = table.Column<decimal>(type: "decimal(65,30)", nullable: false),
                    Status = table.Column<int>(type: "int", nullable: false),
                    Notes = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatorId = table.Column<int>(type: "int", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime(6)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RepairOrders", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RepairOrders_Suppliers_SupplierId",
                        column: x => x.SupplierId,
                        principalTable: "suppliers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_RepairOrders_Users_CreatorId",
                        column: x => x.CreatorId,
                        principalTable: "users",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "RepairItems",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    RepairOrderId = table.Column<int>(type: "int", nullable: false),
                    AssetId = table.Column<int>(type: "int", nullable: false),
                    FaultRecordId = table.Column<int>(type: "int", nullable: true),
                    Description = table.Column<string>(type: "varchar(500)", maxLength: 500, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    RepairCost = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    RepairStatus = table.Column<int>(type: "int", nullable: false),
                    RepairResult = table.Column<string>(type: "varchar(500)", maxLength: 500, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RepairItems", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RepairItems_Assets_AssetId",
                        column: x => x.AssetId,
                        principalTable: "assets",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_RepairItems_FaultRecords_FaultRecordId",
                        column: x => x.FaultRecordId,
                        principalTable: "faultrecords",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_RepairItems_RepairOrders_RepairOrderId",
                        column: x => x.RepairOrderId,
                        principalTable: "RepairOrders",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_quick_memo_categories_user_id",
                table: "quick_memo_categories",
                column: "user_id");

            migrationBuilder.CreateIndex(
                name: "IX_purchaseorders_ApproverId",
                table: "purchaseorders",
                column: "ApproverId");

            migrationBuilder.CreateIndex(
                name: "IX_PeriodicTaskSchedules_TemplateTaskTaskId",
                table: "PeriodicTaskSchedules",
                column: "TemplateTaskTaskId");

            migrationBuilder.CreateIndex(
                name: "IX_PdcaPlans_TaskId",
                table: "PdcaPlans",
                column: "TaskId");

            migrationBuilder.CreateIndex(
                name: "IX_AssetSnapshots_AssetId",
                table: "AssetSnapshots",
                column: "AssetId");

            migrationBuilder.CreateIndex(
                name: "IX_AssetSnapshots_SnapshotDate",
                table: "AssetSnapshots",
                column: "SnapshotDate");

            migrationBuilder.CreateIndex(
                name: "UK_AssetSnapshots_Date_AssetId",
                table: "AssetSnapshots",
                columns: new[] { "SnapshotDate", "AssetId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_periodic_task_schedule_assignees_periodic_task_schedule_id",
                table: "periodic_task_schedule_assignees",
                column: "periodic_task_schedule_id");

            migrationBuilder.CreateIndex(
                name: "IX_RepairItems_AssetId",
                table: "RepairItems",
                column: "AssetId");

            migrationBuilder.CreateIndex(
                name: "IX_RepairItems_FaultRecordId",
                table: "RepairItems",
                column: "FaultRecordId");

            migrationBuilder.CreateIndex(
                name: "IX_RepairItems_RepairOrderId",
                table: "RepairItems",
                column: "RepairOrderId");

            migrationBuilder.CreateIndex(
                name: "IX_RepairItems_RepairStatus",
                table: "RepairItems",
                column: "RepairStatus");

            migrationBuilder.CreateIndex(
                name: "IX_RepairOrders_CreatorId",
                table: "RepairOrders",
                column: "CreatorId");

            migrationBuilder.CreateIndex(
                name: "IX_RepairOrders_OrderCode",
                table: "RepairOrders",
                column: "OrderCode",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_RepairOrders_SendDate",
                table: "RepairOrders",
                column: "SendDate");

            migrationBuilder.CreateIndex(
                name: "IX_RepairOrders_Status",
                table: "RepairOrders",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_RepairOrders_SupplierId",
                table: "RepairOrders",
                column: "SupplierId");

            migrationBuilder.AddForeignKey(
                name: "FK_AssetReceives_purchaseorders_PurchaseOrderId",
                table: "AssetReceives",
                column: "PurchaseOrderId",
                principalTable: "purchaseorders",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AssetReceives_returntofactories_ReturnToFactoryId",
                table: "AssetReceives",
                column: "ReturnToFactoryId",
                principalTable: "returntofactories",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Attachments_Comments_CommentId",
                table: "Attachments",
                column: "CommentId",
                principalTable: "Comments",
                principalColumn: "CommentId");

            migrationBuilder.AddForeignKey(
                name: "FK_Attachments_Tasks_TaskId",
                table: "Attachments",
                column: "TaskId",
                principalTable: "Tasks",
                principalColumn: "TaskId");

            migrationBuilder.AddForeignKey(
                name: "FK_Attachments_users_UploaderUserId",
                table: "Attachments",
                column: "UploaderUserId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Comments_Comments_ParentCommentId",
                table: "Comments",
                column: "ParentCommentId",
                principalTable: "Comments",
                principalColumn: "CommentId");

            migrationBuilder.AddForeignKey(
                name: "FK_Comments_Tasks_TaskId",
                table: "Comments",
                column: "TaskId",
                principalTable: "Tasks",
                principalColumn: "TaskId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Comments_users_UserId",
                table: "Comments",
                column: "UserId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_faultrecords_assets_AssetId",
                table: "faultrecords",
                column: "AssetId",
                principalTable: "assets",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_faultrecords_FaultTypes_FaultTypeId",
                table: "faultrecords",
                column: "FaultTypeId",
                principalTable: "FaultTypes",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_faultrecords_locations_LocationId",
                table: "faultrecords",
                column: "LocationId",
                principalTable: "locations",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_faultrecords_users_AssigneeId",
                table: "faultrecords",
                column: "AssigneeId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_faultrecords_users_ReporterId",
                table: "faultrecords",
                column: "ReporterId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_MaintenanceOrders_faultrecords_FaultRecordId",
                table: "MaintenanceOrders",
                column: "FaultRecordId",
                principalTable: "faultrecords",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_PdcaPlans_Tasks_TaskId",
                table: "PdcaPlans",
                column: "TaskId",
                principalTable: "Tasks",
                principalColumn: "TaskId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_PdcaPlans_users_CreatorUserId",
                table: "PdcaPlans",
                column: "CreatorUserId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_PdcaPlans_users_ResponsiblePersonId",
                table: "PdcaPlans",
                column: "ResponsiblePersonId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_PeriodicTaskSchedules_Tasks_TemplateTaskTaskId",
                table: "PeriodicTaskSchedules",
                column: "TemplateTaskTaskId",
                principalTable: "Tasks",
                principalColumn: "TaskId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_PeriodicTaskSchedules_users_CreatorUserId",
                table: "PeriodicTaskSchedules",
                column: "CreatorUserId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_purchaseitems_assettypes_AssetTypeId",
                table: "purchaseitems",
                column: "AssetTypeId",
                principalTable: "assettypes",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_purchaseitems_purchaseorders_PurchaseOrderId",
                table: "purchaseitems",
                column: "PurchaseOrderId",
                principalTable: "purchaseorders",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_purchaseorders_suppliers_SupplierId",
                table: "purchaseorders",
                column: "SupplierId",
                principalTable: "suppliers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_purchaseorders_users_ApplicantId",
                table: "purchaseorders",
                column: "ApplicantId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_purchaseorders_users_ApproverId",
                table: "purchaseorders",
                column: "ApproverId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_quick_memos_quick_memo_categories_category_id",
                table: "quick_memos",
                column: "category_id",
                principalTable: "quick_memo_categories",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "FK_returntofactories_assets_AssetId",
                table: "returntofactories",
                column: "AssetId",
                principalTable: "assets",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_returntofactories_suppliers_SupplierId",
                table: "returntofactories",
                column: "SupplierId",
                principalTable: "suppliers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_spare_part_transactions_spare_part_locations_location_id",
                table: "spare_part_transactions",
                column: "location_id",
                principalTable: "spare_part_locations",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_spare_part_transactions_spare_parts_part_id",
                table: "spare_part_transactions",
                column: "part_id",
                principalTable: "spare_parts",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_spare_part_types_spare_part_types_parent_id",
                table: "spare_part_types",
                column: "parent_id",
                principalTable: "spare_part_types",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "FK_spare_parts_spare_part_locations_location_id",
                table: "spare_parts",
                column: "location_id",
                principalTable: "spare_part_locations",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "FK_spare_parts_spare_part_types_type_id",
                table: "spare_parts",
                column: "type_id",
                principalTable: "spare_part_types",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_TaskAssignees_Tasks_TaskId",
                table: "TaskAssignees",
                column: "TaskId",
                principalTable: "Tasks",
                principalColumn: "TaskId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_TaskAssignees_users_AssignedByUserId",
                table: "TaskAssignees",
                column: "AssignedByUserId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_TaskAssignees_users_UserId",
                table: "TaskAssignees",
                column: "UserId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_TaskHistories_Attachments_AttachmentId",
                table: "TaskHistories",
                column: "AttachmentId",
                principalTable: "Attachments",
                principalColumn: "AttachmentId");

            migrationBuilder.AddForeignKey(
                name: "FK_TaskHistories_Comments_CommentId",
                table: "TaskHistories",
                column: "CommentId",
                principalTable: "Comments",
                principalColumn: "CommentId");

            migrationBuilder.AddForeignKey(
                name: "FK_TaskHistories_Tasks_TaskId",
                table: "TaskHistories",
                column: "TaskId",
                principalTable: "Tasks",
                principalColumn: "TaskId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_TaskHistories_users_UserId",
                table: "TaskHistories",
                column: "UserId",
                principalTable: "users",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "FK_Tasks_assets_AssetId",
                table: "Tasks",
                column: "AssetId",
                principalTable: "assets",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "FK_Tasks_locations_LocationId",
                table: "Tasks",
                column: "LocationId",
                principalTable: "locations",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "FK_Tasks_Tasks_ParentTaskId",
                table: "Tasks",
                column: "ParentTaskId",
                principalTable: "Tasks",
                principalColumn: "TaskId");

            migrationBuilder.AddForeignKey(
                name: "FK_Tasks_users_AssigneeUserId",
                table: "Tasks",
                column: "AssigneeUserId",
                principalTable: "users",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "FK_Tasks_users_CreatorUserId",
                table: "Tasks",
                column: "CreatorUserId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AssetReceives_purchaseorders_PurchaseOrderId",
                table: "AssetReceives");

            migrationBuilder.DropForeignKey(
                name: "FK_AssetReceives_returntofactories_ReturnToFactoryId",
                table: "AssetReceives");

            migrationBuilder.DropForeignKey(
                name: "FK_Attachments_Comments_CommentId",
                table: "Attachments");

            migrationBuilder.DropForeignKey(
                name: "FK_Attachments_Tasks_TaskId",
                table: "Attachments");

            migrationBuilder.DropForeignKey(
                name: "FK_Attachments_users_UploaderUserId",
                table: "Attachments");

            migrationBuilder.DropForeignKey(
                name: "FK_Comments_Comments_ParentCommentId",
                table: "Comments");

            migrationBuilder.DropForeignKey(
                name: "FK_Comments_Tasks_TaskId",
                table: "Comments");

            migrationBuilder.DropForeignKey(
                name: "FK_Comments_users_UserId",
                table: "Comments");

            migrationBuilder.DropForeignKey(
                name: "FK_faultrecords_assets_AssetId",
                table: "faultrecords");

            migrationBuilder.DropForeignKey(
                name: "FK_faultrecords_FaultTypes_FaultTypeId",
                table: "faultrecords");

            migrationBuilder.DropForeignKey(
                name: "FK_faultrecords_locations_LocationId",
                table: "faultrecords");

            migrationBuilder.DropForeignKey(
                name: "FK_faultrecords_users_AssigneeId",
                table: "faultrecords");

            migrationBuilder.DropForeignKey(
                name: "FK_faultrecords_users_ReporterId",
                table: "faultrecords");

            migrationBuilder.DropForeignKey(
                name: "FK_MaintenanceOrders_faultrecords_FaultRecordId",
                table: "MaintenanceOrders");

            migrationBuilder.DropForeignKey(
                name: "FK_PdcaPlans_Tasks_TaskId",
                table: "PdcaPlans");

            migrationBuilder.DropForeignKey(
                name: "FK_PdcaPlans_users_CreatorUserId",
                table: "PdcaPlans");

            migrationBuilder.DropForeignKey(
                name: "FK_PdcaPlans_users_ResponsiblePersonId",
                table: "PdcaPlans");

            migrationBuilder.DropForeignKey(
                name: "FK_PeriodicTaskSchedules_Tasks_TemplateTaskTaskId",
                table: "PeriodicTaskSchedules");

            migrationBuilder.DropForeignKey(
                name: "FK_PeriodicTaskSchedules_users_CreatorUserId",
                table: "PeriodicTaskSchedules");

            migrationBuilder.DropForeignKey(
                name: "FK_purchaseitems_assettypes_AssetTypeId",
                table: "purchaseitems");

            migrationBuilder.DropForeignKey(
                name: "FK_purchaseitems_purchaseorders_PurchaseOrderId",
                table: "purchaseitems");

            migrationBuilder.DropForeignKey(
                name: "FK_purchaseorders_suppliers_SupplierId",
                table: "purchaseorders");

            migrationBuilder.DropForeignKey(
                name: "FK_purchaseorders_users_ApplicantId",
                table: "purchaseorders");

            migrationBuilder.DropForeignKey(
                name: "FK_purchaseorders_users_ApproverId",
                table: "purchaseorders");

            migrationBuilder.DropForeignKey(
                name: "FK_quick_memos_quick_memo_categories_category_id",
                table: "quick_memos");

            migrationBuilder.DropForeignKey(
                name: "FK_returntofactories_assets_AssetId",
                table: "returntofactories");

            migrationBuilder.DropForeignKey(
                name: "FK_returntofactories_suppliers_SupplierId",
                table: "returntofactories");

            migrationBuilder.DropForeignKey(
                name: "FK_spare_part_transactions_spare_part_locations_location_id",
                table: "spare_part_transactions");

            migrationBuilder.DropForeignKey(
                name: "FK_spare_part_transactions_spare_parts_part_id",
                table: "spare_part_transactions");

            migrationBuilder.DropForeignKey(
                name: "FK_spare_part_types_spare_part_types_parent_id",
                table: "spare_part_types");

            migrationBuilder.DropForeignKey(
                name: "FK_spare_parts_spare_part_locations_location_id",
                table: "spare_parts");

            migrationBuilder.DropForeignKey(
                name: "FK_spare_parts_spare_part_types_type_id",
                table: "spare_parts");

            migrationBuilder.DropForeignKey(
                name: "FK_TaskAssignees_Tasks_TaskId",
                table: "TaskAssignees");

            migrationBuilder.DropForeignKey(
                name: "FK_TaskAssignees_users_AssignedByUserId",
                table: "TaskAssignees");

            migrationBuilder.DropForeignKey(
                name: "FK_TaskAssignees_users_UserId",
                table: "TaskAssignees");

            migrationBuilder.DropForeignKey(
                name: "FK_TaskHistories_Attachments_AttachmentId",
                table: "TaskHistories");

            migrationBuilder.DropForeignKey(
                name: "FK_TaskHistories_Comments_CommentId",
                table: "TaskHistories");

            migrationBuilder.DropForeignKey(
                name: "FK_TaskHistories_Tasks_TaskId",
                table: "TaskHistories");

            migrationBuilder.DropForeignKey(
                name: "FK_TaskHistories_users_UserId",
                table: "TaskHistories");

            migrationBuilder.DropForeignKey(
                name: "FK_Tasks_assets_AssetId",
                table: "Tasks");

            migrationBuilder.DropForeignKey(
                name: "FK_Tasks_locations_LocationId",
                table: "Tasks");

            migrationBuilder.DropForeignKey(
                name: "FK_Tasks_Tasks_ParentTaskId",
                table: "Tasks");

            migrationBuilder.DropForeignKey(
                name: "FK_Tasks_users_AssigneeUserId",
                table: "Tasks");

            migrationBuilder.DropForeignKey(
                name: "FK_Tasks_users_CreatorUserId",
                table: "Tasks");

            migrationBuilder.DropTable(
                name: "AssetSnapshots");

            migrationBuilder.DropTable(
                name: "notifications");

            migrationBuilder.DropTable(
                name: "periodic_task_schedule_assignees");

            migrationBuilder.DropTable(
                name: "RepairItems");

            migrationBuilder.DropTable(
                name: "RepairOrders");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Tasks",
                table: "Tasks");

            migrationBuilder.DropPrimaryKey(
                name: "PK_TaskAssignees",
                table: "TaskAssignees");

            migrationBuilder.DropPrimaryKey(
                name: "PK_suppliers",
                table: "suppliers");

            migrationBuilder.DropPrimaryKey(
                name: "PK_returntofactories",
                table: "returntofactories");

            migrationBuilder.DropIndex(
                name: "IX_quick_memo_categories_user_id",
                table: "quick_memo_categories");

            migrationBuilder.DropPrimaryKey(
                name: "PK_purchaseorders",
                table: "purchaseorders");

            migrationBuilder.DropIndex(
                name: "IX_purchaseorders_ApproverId",
                table: "purchaseorders");

            migrationBuilder.DropPrimaryKey(
                name: "PK_purchaseitems",
                table: "purchaseitems");

            migrationBuilder.DropPrimaryKey(
                name: "PK_PeriodicTaskSchedules",
                table: "PeriodicTaskSchedules");

            migrationBuilder.DropIndex(
                name: "IX_PeriodicTaskSchedules_TemplateTaskTaskId",
                table: "PeriodicTaskSchedules");

            migrationBuilder.DropPrimaryKey(
                name: "PK_PdcaPlans",
                table: "PdcaPlans");

            migrationBuilder.DropIndex(
                name: "IX_PdcaPlans_TaskId",
                table: "PdcaPlans");

            migrationBuilder.DropPrimaryKey(
                name: "PK_faultrecords",
                table: "faultrecords");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Comments",
                table: "Comments");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Attachments",
                table: "Attachments");

            migrationBuilder.DropPrimaryKey(
                name: "PK_TaskHistories",
                table: "TaskHistories");

            migrationBuilder.DropColumn(
                name: "material_number",
                table: "spare_parts");

            migrationBuilder.DropColumn(
                name: "ActualReturnTime",
                table: "returntofactories");

            migrationBuilder.DropColumn(
                name: "Code",
                table: "returntofactories");

            migrationBuilder.DropColumn(
                name: "FaultRecordId",
                table: "returntofactories");

            migrationBuilder.DropColumn(
                name: "InWarranty",
                table: "returntofactories");

            migrationBuilder.DropColumn(
                name: "RepairCost",
                table: "returntofactories");

            migrationBuilder.DropColumn(
                name: "ApplicationTime",
                table: "purchaseorders");

            migrationBuilder.DropColumn(
                name: "ApprovalTime",
                table: "purchaseorders");

            migrationBuilder.DropColumn(
                name: "ApproverId",
                table: "purchaseorders");

            migrationBuilder.DropColumn(
                name: "Description",
                table: "purchaseorders");

            migrationBuilder.DropColumn(
                name: "Title",
                table: "purchaseorders");

            migrationBuilder.DropColumn(
                name: "ItemCode",
                table: "purchaseitems");

            migrationBuilder.DropColumn(
                name: "TemplateTaskTaskId",
                table: "PeriodicTaskSchedules");

            migrationBuilder.RenameTable(
                name: "Tasks",
                newName: "tasks");

            migrationBuilder.RenameTable(
                name: "TaskAssignees",
                newName: "taskassignees");

            migrationBuilder.RenameTable(
                name: "suppliers",
                newName: "Suppliers");

            migrationBuilder.RenameTable(
                name: "returntofactories",
                newName: "ReturnToFactories");

            migrationBuilder.RenameTable(
                name: "purchaseorders",
                newName: "PurchaseOrders");

            migrationBuilder.RenameTable(
                name: "purchaseitems",
                newName: "PurchaseItems");

            migrationBuilder.RenameTable(
                name: "PeriodicTaskSchedules",
                newName: "periodictaskschedules");

            migrationBuilder.RenameTable(
                name: "PdcaPlans",
                newName: "pdcaplans");

            migrationBuilder.RenameTable(
                name: "faultrecords",
                newName: "FaultRecords");

            migrationBuilder.RenameTable(
                name: "Comments",
                newName: "comments");

            migrationBuilder.RenameTable(
                name: "Attachments",
                newName: "attachments");

            migrationBuilder.RenameTable(
                name: "TaskHistories",
                newName: "taskhistory");

            migrationBuilder.RenameIndex(
                name: "IX_Tasks_ParentTaskId",
                table: "tasks",
                newName: "IX_tasks_ParentTaskId");

            migrationBuilder.RenameIndex(
                name: "IX_Tasks_LocationId",
                table: "tasks",
                newName: "IX_tasks_LocationId");

            migrationBuilder.RenameIndex(
                name: "IX_Tasks_CreatorUserId",
                table: "tasks",
                newName: "IX_tasks_CreatorUserId");

            migrationBuilder.RenameIndex(
                name: "IX_Tasks_AssigneeUserId",
                table: "tasks",
                newName: "IX_tasks_AssigneeUserId");

            migrationBuilder.RenameIndex(
                name: "IX_Tasks_AssetId",
                table: "tasks",
                newName: "IX_tasks_AssetId");

            migrationBuilder.RenameIndex(
                name: "IX_TaskAssignees_UserId",
                table: "taskassignees",
                newName: "IX_taskassignees_UserId");

            migrationBuilder.RenameIndex(
                name: "IX_TaskAssignees_TaskId",
                table: "taskassignees",
                newName: "IX_taskassignees_TaskId");

            migrationBuilder.RenameIndex(
                name: "IX_TaskAssignees_AssignedByUserId",
                table: "taskassignees",
                newName: "IX_taskassignees_AssignedByUserId");

            migrationBuilder.RenameColumn(
                name: "SenderId",
                table: "ReturnToFactories",
                newName: "OperatorId");

            migrationBuilder.RenameColumn(
                name: "SendTime",
                table: "ReturnToFactories",
                newName: "ExpectedReturnDate");

            migrationBuilder.RenameColumn(
                name: "RepairResult",
                table: "ReturnToFactories",
                newName: "Result");

            migrationBuilder.RenameColumn(
                name: "Notes",
                table: "ReturnToFactories",
                newName: "Remarks");

            migrationBuilder.RenameColumn(
                name: "EstimatedReturnTime",
                table: "ReturnToFactories",
                newName: "ActualReturnDate");

            migrationBuilder.RenameIndex(
                name: "IX_returntofactories_SupplierId",
                table: "ReturnToFactories",
                newName: "IX_ReturnToFactories_SupplierId");

            migrationBuilder.RenameIndex(
                name: "IX_returntofactories_AssetId",
                table: "ReturnToFactories",
                newName: "IX_ReturnToFactories_AssetId");

            migrationBuilder.RenameIndex(
                name: "IX_quick_memos_user_id",
                table: "quick_memos",
                newName: "ix_quick_memos_user_id");

            migrationBuilder.RenameColumn(
                name: "OrderCode",
                table: "PurchaseOrders",
                newName: "OrderNumber");

            migrationBuilder.RenameColumn(
                name: "EstimatedDeliveryDate",
                table: "PurchaseOrders",
                newName: "ExpectedDeliveryDate");

            migrationBuilder.RenameColumn(
                name: "ApplicantId",
                table: "PurchaseOrders",
                newName: "RequesterId");

            migrationBuilder.RenameIndex(
                name: "IX_purchaseorders_SupplierId",
                table: "PurchaseOrders",
                newName: "IX_PurchaseOrders_SupplierId");

            migrationBuilder.RenameIndex(
                name: "IX_purchaseorders_ApplicantId",
                table: "PurchaseOrders",
                newName: "IX_PurchaseOrders_RequesterId");

            migrationBuilder.RenameColumn(
                name: "ItemName",
                table: "PurchaseItems",
                newName: "Name");

            migrationBuilder.RenameIndex(
                name: "IX_purchaseitems_PurchaseOrderId",
                table: "PurchaseItems",
                newName: "IX_PurchaseItems_PurchaseOrderId");

            migrationBuilder.RenameIndex(
                name: "IX_purchaseitems_AssetTypeId",
                table: "PurchaseItems",
                newName: "IX_PurchaseItems_AssetTypeId");

            migrationBuilder.RenameColumn(
                name: "Status",
                table: "periodictaskschedules",
                newName: "status");

            migrationBuilder.RenameColumn(
                name: "Name",
                table: "periodictaskschedules",
                newName: "name");

            migrationBuilder.RenameColumn(
                name: "Description",
                table: "periodictaskschedules",
                newName: "description");

            migrationBuilder.RenameColumn(
                name: "WeekOfMonth",
                table: "periodictaskschedules",
                newName: "week_of_month");

            migrationBuilder.RenameColumn(
                name: "TotalOccurrences",
                table: "periodictaskschedules",
                newName: "total_occurrences");

            migrationBuilder.RenameColumn(
                name: "TemplateTaskId",
                table: "periodictaskschedules",
                newName: "template_task_id");

            migrationBuilder.RenameColumn(
                name: "StartDate",
                table: "periodictaskschedules",
                newName: "start_date");

            migrationBuilder.RenameColumn(
                name: "RecurrenceType",
                table: "periodictaskschedules",
                newName: "recurrence_type");

            migrationBuilder.RenameColumn(
                name: "RecurrenceInterval",
                table: "periodictaskschedules",
                newName: "recurrence_interval");

            migrationBuilder.RenameColumn(
                name: "OccurrencesGenerated",
                table: "periodictaskschedules",
                newName: "occurrences_generated");

            migrationBuilder.RenameColumn(
                name: "NextGenerationTime",
                table: "periodictaskschedules",
                newName: "next_generation_time");

            migrationBuilder.RenameColumn(
                name: "MonthOfYear",
                table: "periodictaskschedules",
                newName: "month_of_year");

            migrationBuilder.RenameColumn(
                name: "LastUpdatedTimestamp",
                table: "periodictaskschedules",
                newName: "last_updated_timestamp");

            migrationBuilder.RenameColumn(
                name: "LastGeneratedTimestamp",
                table: "periodictaskschedules",
                newName: "last_generated_timestamp");

            migrationBuilder.RenameColumn(
                name: "LastError",
                table: "periodictaskschedules",
                newName: "last_error");

            migrationBuilder.RenameColumn(
                name: "EndDate",
                table: "periodictaskschedules",
                newName: "end_date");

            migrationBuilder.RenameColumn(
                name: "EndConditionType",
                table: "periodictaskschedules",
                newName: "end_condition_type");

            migrationBuilder.RenameColumn(
                name: "DefaultPoints",
                table: "periodictaskschedules",
                newName: "default_points");

            migrationBuilder.RenameColumn(
                name: "DaysOfWeek",
                table: "periodictaskschedules",
                newName: "days_of_week");

            migrationBuilder.RenameColumn(
                name: "DayOfWeekForMonth",
                table: "periodictaskschedules",
                newName: "day_of_week_for_month");

            migrationBuilder.RenameColumn(
                name: "DayOfMonth",
                table: "periodictaskschedules",
                newName: "day_of_month");

            migrationBuilder.RenameColumn(
                name: "CronExpression",
                table: "periodictaskschedules",
                newName: "cron_expression");

            migrationBuilder.RenameColumn(
                name: "CreatorUserId",
                table: "periodictaskschedules",
                newName: "creator_user_id");

            migrationBuilder.RenameColumn(
                name: "CreationTimestamp",
                table: "periodictaskschedules",
                newName: "creation_timestamp");

            migrationBuilder.RenameColumn(
                name: "PeriodicTaskScheduleId",
                table: "periodictaskschedules",
                newName: "periodic_task_schedule_id");

            migrationBuilder.RenameIndex(
                name: "IX_PeriodicTaskSchedules_CreatorUserId",
                table: "periodictaskschedules",
                newName: "IX_periodictaskschedules_creator_user_id");

            migrationBuilder.RenameColumn(
                name: "Title",
                table: "pdcaplans",
                newName: "title");

            migrationBuilder.RenameColumn(
                name: "Status",
                table: "pdcaplans",
                newName: "status");

            migrationBuilder.RenameColumn(
                name: "Stage",
                table: "pdcaplans",
                newName: "stage");

            migrationBuilder.RenameColumn(
                name: "Notes",
                table: "pdcaplans",
                newName: "notes");

            migrationBuilder.RenameColumn(
                name: "Goal",
                table: "pdcaplans",
                newName: "goal");

            migrationBuilder.RenameColumn(
                name: "ResponsiblePersonId",
                table: "pdcaplans",
                newName: "responsible_person_id");

            migrationBuilder.RenameColumn(
                name: "PlanContent",
                table: "pdcaplans",
                newName: "plan_content");

            migrationBuilder.RenameColumn(
                name: "LastUpdatedTimestamp",
                table: "pdcaplans",
                newName: "last_updated_timestamp");

            migrationBuilder.RenameColumn(
                name: "DoRecord",
                table: "pdcaplans",
                newName: "do_record");

            migrationBuilder.RenameColumn(
                name: "CreatorUserId",
                table: "pdcaplans",
                newName: "creator_user_id");

            migrationBuilder.RenameColumn(
                name: "CreationTimestamp",
                table: "pdcaplans",
                newName: "creation_timestamp");

            migrationBuilder.RenameColumn(
                name: "CompletionRate",
                table: "pdcaplans",
                newName: "completion_rate");

            migrationBuilder.RenameColumn(
                name: "CheckResult",
                table: "pdcaplans",
                newName: "check_result");

            migrationBuilder.RenameColumn(
                name: "ActAction",
                table: "pdcaplans",
                newName: "act_action");

            migrationBuilder.RenameColumn(
                name: "PdcaPlanId",
                table: "pdcaplans",
                newName: "pdca_plan_id");

            migrationBuilder.RenameIndex(
                name: "IX_PdcaPlans_ResponsiblePersonId",
                table: "pdcaplans",
                newName: "IX_pdcaplans_responsible_person_id");

            migrationBuilder.RenameIndex(
                name: "IX_PdcaPlans_CreatorUserId",
                table: "pdcaplans",
                newName: "IX_pdcaplans_creator_user_id");

            migrationBuilder.RenameIndex(
                name: "IX_faultrecords_ReporterId",
                table: "FaultRecords",
                newName: "IX_FaultRecords_ReporterId");

            migrationBuilder.RenameIndex(
                name: "IX_faultrecords_LocationId",
                table: "FaultRecords",
                newName: "IX_FaultRecords_LocationId");

            migrationBuilder.RenameIndex(
                name: "IX_faultrecords_FaultTypeId",
                table: "FaultRecords",
                newName: "IX_FaultRecords_FaultTypeId");

            migrationBuilder.RenameIndex(
                name: "IX_faultrecords_AssigneeId",
                table: "FaultRecords",
                newName: "IX_FaultRecords_AssigneeId");

            migrationBuilder.RenameIndex(
                name: "IX_faultrecords_AssetId",
                table: "FaultRecords",
                newName: "IX_FaultRecords_AssetId");

            migrationBuilder.RenameIndex(
                name: "IX_Comments_UserId",
                table: "comments",
                newName: "IX_comments_UserId");

            migrationBuilder.RenameIndex(
                name: "IX_Comments_TaskId",
                table: "comments",
                newName: "IX_comments_TaskId");

            migrationBuilder.RenameIndex(
                name: "IX_Comments_ParentCommentId",
                table: "comments",
                newName: "IX_comments_ParentCommentId");

            migrationBuilder.RenameIndex(
                name: "IX_Attachments_UploaderUserId",
                table: "attachments",
                newName: "IX_attachments_UploaderUserId");

            migrationBuilder.RenameIndex(
                name: "IX_Attachments_TaskId",
                table: "attachments",
                newName: "IX_attachments_TaskId");

            migrationBuilder.RenameIndex(
                name: "IX_Attachments_CommentId",
                table: "attachments",
                newName: "IX_attachments_CommentId");

            migrationBuilder.RenameIndex(
                name: "IX_TaskHistories_UserId",
                table: "taskhistory",
                newName: "IX_taskhistory_UserId");

            migrationBuilder.RenameIndex(
                name: "IX_TaskHistories_TaskId",
                table: "taskhistory",
                newName: "IX_taskhistory_TaskId");

            migrationBuilder.RenameIndex(
                name: "IX_TaskHistories_CommentId",
                table: "taskhistory",
                newName: "IX_taskhistory_CommentId");

            migrationBuilder.RenameIndex(
                name: "IX_TaskHistories_AttachmentId",
                table: "taskhistory",
                newName: "IX_taskhistory_AttachmentId");

            migrationBuilder.AddColumn<int>(
                name: "CreatedBy",
                table: "ReturnToFactories",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Reason",
                table: "ReturnToFactories",
                type: "varchar(500)",
                maxLength: 500,
                nullable: false,
                defaultValue: "")
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<DateTime>(
                name: "ReturnDate",
                table: "ReturnToFactories",
                type: "datetime(6)",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<int>(
                name: "UpdatedBy",
                table: "ReturnToFactories",
                type: "int",
                nullable: true);

            migrationBuilder.AlterColumn<bool>(
                name: "is_pinned",
                table: "quick_memos",
                type: "tinyint(1)",
                nullable: false,
                defaultValue: false,
                oldClrType: typeof(bool),
                oldType: "tinyint(1)");

            migrationBuilder.AlterColumn<int>(
                name: "AssetTypeId",
                table: "PurchaseItems",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsAssetGenerated",
                table: "PurchaseItems",
                type: "tinyint(1)",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AlterColumn<int>(
                name: "AssetId",
                table: "FaultRecords",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AddPrimaryKey(
                name: "PK_tasks",
                table: "tasks",
                column: "TaskId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_taskassignees",
                table: "taskassignees",
                column: "TaskAssigneeId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Suppliers",
                table: "Suppliers",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_ReturnToFactories",
                table: "ReturnToFactories",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_PurchaseOrders",
                table: "PurchaseOrders",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_PurchaseItems",
                table: "PurchaseItems",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_periodictaskschedules",
                table: "periodictaskschedules",
                column: "periodic_task_schedule_id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_pdcaplans",
                table: "pdcaplans",
                column: "pdca_plan_id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_FaultRecords",
                table: "FaultRecords",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_comments",
                table: "comments",
                column: "CommentId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_attachments",
                table: "attachments",
                column: "AttachmentId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_taskhistory",
                table: "taskhistory",
                column: "TaskHistoryId");

            migrationBuilder.CreateIndex(
                name: "IX_tasks_PreviousInstanceTaskId",
                table: "tasks",
                column: "PreviousInstanceTaskId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_spare_parts_code",
                table: "spare_parts",
                column: "code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_spare_parts_quantity",
                table: "spare_parts",
                column: "quantity");

            migrationBuilder.CreateIndex(
                name: "IX_spare_part_types_code",
                table: "spare_part_types",
                column: "code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_spare_part_types_path",
                table: "spare_part_types",
                column: "path");

            migrationBuilder.CreateIndex(
                name: "IX_spare_part_transactions_batch_number",
                table: "spare_part_transactions",
                column: "batch_number");

            migrationBuilder.CreateIndex(
                name: "IX_spare_part_transactions_transaction_time",
                table: "spare_part_transactions",
                column: "transaction_time");

            migrationBuilder.CreateIndex(
                name: "IX_spare_part_transactions_type",
                table: "spare_part_transactions",
                column: "type");

            migrationBuilder.CreateIndex(
                name: "IX_spare_part_locations_area",
                table: "spare_part_locations",
                column: "area");

            migrationBuilder.CreateIndex(
                name: "IX_spare_part_locations_code",
                table: "spare_part_locations",
                column: "code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ReturnToFactories_OperatorId",
                table: "ReturnToFactories",
                column: "OperatorId");

            migrationBuilder.CreateIndex(
                name: "ix_quick_memos_user_category",
                table: "quick_memos",
                columns: new[] { "user_id", "category_id" });

            migrationBuilder.CreateIndex(
                name: "ix_quick_memos_user_pinned_updated",
                table: "quick_memos",
                columns: new[] { "user_id", "is_pinned", "updated_at" });

            migrationBuilder.CreateIndex(
                name: "ix_quick_memo_categories_user_name",
                table: "quick_memo_categories",
                columns: new[] { "user_id", "name" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_periodictaskschedules_template_task_id",
                table: "periodictaskschedules",
                column: "template_task_id");

            migrationBuilder.CreateIndex(
                name: "IX_pdcaplans_TaskId",
                table: "pdcaplans",
                column: "TaskId",
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_AssetReceives_PurchaseOrders_PurchaseOrderId",
                table: "AssetReceives",
                column: "PurchaseOrderId",
                principalTable: "PurchaseOrders",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AssetReceives_ReturnToFactories_ReturnToFactoryId",
                table: "AssetReceives",
                column: "ReturnToFactoryId",
                principalTable: "ReturnToFactories",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_attachments_comments_CommentId",
                table: "attachments",
                column: "CommentId",
                principalTable: "comments",
                principalColumn: "CommentId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_attachments_tasks_TaskId",
                table: "attachments",
                column: "TaskId",
                principalTable: "tasks",
                principalColumn: "TaskId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_attachments_users_UploaderUserId",
                table: "attachments",
                column: "UploaderUserId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_comments_comments_ParentCommentId",
                table: "comments",
                column: "ParentCommentId",
                principalTable: "comments",
                principalColumn: "CommentId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_comments_tasks_TaskId",
                table: "comments",
                column: "TaskId",
                principalTable: "tasks",
                principalColumn: "TaskId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_comments_users_UserId",
                table: "comments",
                column: "UserId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_FaultRecords_assets_AssetId",
                table: "FaultRecords",
                column: "AssetId",
                principalTable: "assets",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_FaultRecords_FaultTypes_FaultTypeId",
                table: "FaultRecords",
                column: "FaultTypeId",
                principalTable: "FaultTypes",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_FaultRecords_locations_LocationId",
                table: "FaultRecords",
                column: "LocationId",
                principalTable: "locations",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "FK_FaultRecords_users_AssigneeId",
                table: "FaultRecords",
                column: "AssigneeId",
                principalTable: "users",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "FK_FaultRecords_users_ReporterId",
                table: "FaultRecords",
                column: "ReporterId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_MaintenanceOrders_FaultRecords_FaultRecordId",
                table: "MaintenanceOrders",
                column: "FaultRecordId",
                principalTable: "FaultRecords",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_pdcaplans_tasks_TaskId",
                table: "pdcaplans",
                column: "TaskId",
                principalTable: "tasks",
                principalColumn: "TaskId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_pdcaplans_users_creator_user_id",
                table: "pdcaplans",
                column: "creator_user_id",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_pdcaplans_users_responsible_person_id",
                table: "pdcaplans",
                column: "responsible_person_id",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_periodictaskschedules_tasks_template_task_id",
                table: "periodictaskschedules",
                column: "template_task_id",
                principalTable: "tasks",
                principalColumn: "TaskId",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_periodictaskschedules_users_creator_user_id",
                table: "periodictaskschedules",
                column: "creator_user_id",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_PurchaseItems_assettypes_AssetTypeId",
                table: "PurchaseItems",
                column: "AssetTypeId",
                principalTable: "assettypes",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_PurchaseItems_PurchaseOrders_PurchaseOrderId",
                table: "PurchaseItems",
                column: "PurchaseOrderId",
                principalTable: "PurchaseOrders",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_PurchaseOrders_Suppliers_SupplierId",
                table: "PurchaseOrders",
                column: "SupplierId",
                principalTable: "Suppliers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_PurchaseOrders_users_RequesterId",
                table: "PurchaseOrders",
                column: "RequesterId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_quick_memos_quick_memo_categories_category_id",
                table: "quick_memos",
                column: "category_id",
                principalTable: "quick_memo_categories",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_ReturnToFactories_assets_AssetId",
                table: "ReturnToFactories",
                column: "AssetId",
                principalTable: "assets",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_ReturnToFactories_Suppliers_SupplierId",
                table: "ReturnToFactories",
                column: "SupplierId",
                principalTable: "Suppliers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_ReturnToFactories_users_OperatorId",
                table: "ReturnToFactories",
                column: "OperatorId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_spare_part_transactions_spare_part_locations_location_id",
                table: "spare_part_transactions",
                column: "location_id",
                principalTable: "spare_part_locations",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_spare_part_transactions_spare_parts_part_id",
                table: "spare_part_transactions",
                column: "part_id",
                principalTable: "spare_parts",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_spare_part_types_spare_part_types_parent_id",
                table: "spare_part_types",
                column: "parent_id",
                principalTable: "spare_part_types",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_spare_parts_spare_part_locations_location_id",
                table: "spare_parts",
                column: "location_id",
                principalTable: "spare_part_locations",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_spare_parts_spare_part_types_type_id",
                table: "spare_parts",
                column: "type_id",
                principalTable: "spare_part_types",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_taskassignees_tasks_TaskId",
                table: "taskassignees",
                column: "TaskId",
                principalTable: "tasks",
                principalColumn: "TaskId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_taskassignees_users_AssignedByUserId",
                table: "taskassignees",
                column: "AssignedByUserId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_taskassignees_users_UserId",
                table: "taskassignees",
                column: "UserId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_taskhistory_attachments_AttachmentId",
                table: "taskhistory",
                column: "AttachmentId",
                principalTable: "attachments",
                principalColumn: "AttachmentId",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_taskhistory_comments_CommentId",
                table: "taskhistory",
                column: "CommentId",
                principalTable: "comments",
                principalColumn: "CommentId",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_taskhistory_tasks_TaskId",
                table: "taskhistory",
                column: "TaskId",
                principalTable: "tasks",
                principalColumn: "TaskId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_taskhistory_users_UserId",
                table: "taskhistory",
                column: "UserId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_tasks_assets_AssetId",
                table: "tasks",
                column: "AssetId",
                principalTable: "assets",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_tasks_locations_LocationId",
                table: "tasks",
                column: "LocationId",
                principalTable: "locations",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_tasks_tasks_ParentTaskId",
                table: "tasks",
                column: "ParentTaskId",
                principalTable: "tasks",
                principalColumn: "TaskId",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_tasks_tasks_PreviousInstanceTaskId",
                table: "tasks",
                column: "PreviousInstanceTaskId",
                principalTable: "tasks",
                principalColumn: "TaskId",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_tasks_users_AssigneeUserId",
                table: "tasks",
                column: "AssigneeUserId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_tasks_users_CreatorUserId",
                table: "tasks",
                column: "CreatorUserId",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
