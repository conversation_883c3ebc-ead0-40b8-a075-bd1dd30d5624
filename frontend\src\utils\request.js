/**
 * HTTP请求工具
 * 文件路径: src/utils/request.js
 * 功能描述: 封装axios，处理请求拦截和响应拦截
 */

import axios from 'axios'
import { ElMessage } from 'element-plus'
import { getToken, removeToken } from '@/utils/auth'
import router from '@/router'

// 创建axios实例
const service = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api', // 使用环境变量，提供默认值
  timeout: 15000
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 添加token到请求头
    const token = getToken()
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    
    // 添加详细调试日志
    console.log('API请求详情:', {
      url: config.url,
      method: config.method,
      baseURL: config.baseURL,
      完整URL: `${config.baseURL}${config.url}`,
      params: config.params,
      data: config.data
    })
    
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    const { config, status, data } = response
    const { method, url } = config
    
    console.log(`API响应: ${method.toUpperCase()} ${url} - 状态: ${status}`)
    
    // 针对资产API的额外日志
    if (url.includes('/Asset')) {
      console.log('---资产API详细响应信息---')
      
      if (data) {
        if (data.items && Array.isArray(data.items)) {
          console.log(`总数据量: ${data.total || '未知'}`)
          console.log(`当前页数据项: ${data.items.length}项`)
          if (data.items.length > 0) {
            console.log('数据示例 (第一项):', data.items[0])
          }
        } else if (Array.isArray(data)) {
          console.log(`数组数据: 共${data.length}项`)
          if (data.length > 0) {
            console.log('数据示例 (第一项):', data[0])
          }
        } else if (data.data) {
          // 处理data.data格式
          const innerData = data.data
          if (Array.isArray(innerData)) {
            console.log(`内部数组数据: 共${innerData.length}项`)
            console.log('总数据量:', data.total || data.totalCount || innerData.length)
            if (innerData.length > 0) {
              console.log('数据示例 (第一项):', innerData[0])
            }
          } else if (innerData.items && Array.isArray(innerData.items)) {
            console.log(`内部分页数据: 共${innerData.items.length}项`)
            console.log('总数据量:', innerData.total || innerData.totalCount || '未知')
            if (innerData.items.length > 0) {
              console.log('数据示例 (第一项):', innerData.items[0])
            }
          }
        }
      }
      
      console.log('完整响应数据:', data)
      console.log('---资产API日志结束---')
    }
    
    return response.data
  },
  error => {
    console.error('响应错误详情:', {
      message: error.message,
      config: error.config,
      code: error.code,
      status: error.response?.status,
      statusText: error.response?.statusText,
      responseData: error.response?.data
    })
    
    if (error.response) {
      const { status, data } = error.response
      
      // 处理401未授权错误
      if (status === 401) {
        // 清除token并重定向到登录页
        removeToken()
        router.push('/login')
        ElMessage.error('登录已过期，请重新登录')
      } 
      // 处理403禁止访问错误
      else if (status === 403) {
        ElMessage.error('没有权限访问此资源')
      }
      // 处理404资源不存在错误
      else if (status === 404) {
        ElMessage.error(`请求的资源不存在: ${error.config.url}`)
      }
      // 处理其他错误
      else {
        const message = data?.message || '请求失败，请稍后重试'
        ElMessage.error(message)
      }
    } else {
      // 网络错误或其他错误
      ElMessage.error('网络错误，请检查您的网络连接')
    }
    
    return Promise.reject(error)
  }
)

// 封装请求方法
const request = {
  get(url, config) {
    return service.get(url, config)
  },
  post(url, data, config) {
    return service.post(url, data, config)
  },
  put(url, data, config) {
    return service.put(url, data, config)
  },
  patch(url, data, config) {
    return service.patch(url, data, config)
  },
  delete(url, config) {
    return service.delete(url, config)
  }
}

export default service 