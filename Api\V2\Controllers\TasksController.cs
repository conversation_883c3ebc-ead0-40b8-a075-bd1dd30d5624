// File: Api/V2/TasksController.cs
// Description: 任务管理API控制器

using System;
using System.Collections.Generic;
using System.IO;
using System.Security.Claims;
using System.Threading.Tasks;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Application.Features.Tasks.Dtos;
using ItAssetsSystem.Application.Features.Tasks.Services;
using ItAssetsSystem.Core.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace ItAssetsSystem.Api.V2
{
    /// <summary>
    /// 任务管理API控制器
    /// </summary>
    [ApiController]
    [Route("api/v2/tasks")]
    [Authorize] // 启用认证
    public class TasksController : ControllerBase
    {
        private readonly ITaskService _taskService;
        private readonly ILogger<TasksController> _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="taskService">任务服务</param>
        /// <param name="logger">日志记录器</param>
        public TasksController(ITaskService taskService, ILogger<TasksController> logger)
        {
            _taskService = taskService ?? throw new ArgumentNullException(nameof(taskService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 获取当前用户ID
        /// </summary>
        /// <returns>当前用户ID</returns>
        private int GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
            if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
            {
                throw new UnauthorizedAccessException("未能获取有效的用户ID");
            }
            return userId;
        }

        /// <summary>
        /// 获取任务列表（分页）
        /// </summary>
        /// <param name="parameters">查询参数</param>
        /// <returns>任务列表</returns>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<List<TaskDto>>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<List<TaskDto>>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<List<TaskDto>>))]
        public async Task<IActionResult> GetTasks([FromQuery] TaskQueryParametersDto parameters)
        {
            try
            {
                var result = await _taskService.GetTasksAsync(parameters);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务列表时发生错误");
                return StatusCode(StatusCodes.Status500InternalServerError, 
                    ApiResponseFactory.CreateFail<List<TaskDto>>("获取任务列表时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 获取任务详情
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <returns>任务详情</returns>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<TaskDto>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<TaskDto>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<TaskDto>))]
        public async Task<IActionResult> GetTask(long id)
        {
            try
            {
                var result = await _taskService.GetTaskByIdAsync(id);
                if (!result.Success)
                {
                    return NotFound(result);
                }
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务详情时发生错误: ID={TaskId}", id);
                return StatusCode(StatusCodes.Status500InternalServerError, 
                    ApiResponseFactory.CreateFail<TaskDto>("获取任务详情时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 创建任务
        /// </summary>
        /// <param name="request">创建任务请求</param>
        /// <returns>创建的任务</returns>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created, Type = typeof(ApiResponse<TaskDto>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<TaskDto>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<TaskDto>))]
        public async Task<IActionResult> CreateTask([FromBody] CreateTaskRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponseFactory.CreateFail<TaskDto>("请求数据无效"));
                }

                var currentUserId = GetCurrentUserId();
                var result = await _taskService.CreateTaskAsync(request, currentUserId);
                
                if (!result.Success)
                {
                    return BadRequest(result);
                }

                return CreatedAtAction(nameof(GetTask), new { id = result.Data.TaskId }, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建任务时发生错误");
                return StatusCode(StatusCodes.Status500InternalServerError, 
                    ApiResponseFactory.CreateFail<TaskDto>("创建任务时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 更新任务
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <param name="request">更新任务请求</param>
        /// <returns>更新后的任务</returns>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<TaskDto>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<TaskDto>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<TaskDto>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<TaskDto>))]
        public async Task<IActionResult> UpdateTask(long id, [FromBody] UpdateTaskRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponseFactory.CreateFail<TaskDto>("请求数据无效"));
                }

                var currentUserId = GetCurrentUserId();
                var result = await _taskService.UpdateTaskAsync(id, request, currentUserId);
                
                if (!result.Success)
                {
                    return result.Error.Contains("未找到") ? NotFound(result) : BadRequest(result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新任务时发生错误: ID={TaskId}", id);
                return StatusCode(StatusCodes.Status500InternalServerError, 
                    ApiResponseFactory.CreateFail<TaskDto>("更新任务时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 删除任务
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <returns>操作结果</returns>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<bool>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<bool>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<bool>))]
        public async Task<IActionResult> DeleteTask(long id)
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                var result = await _taskService.DeleteTaskAsync(id, currentUserId);
                
                if (!result.Success)
                {
                    return result.Error.Contains("未找到") ? NotFound(result) : BadRequest(result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除任务时发生错误: ID={TaskId}", id);
                return StatusCode(StatusCodes.Status500InternalServerError, 
                    ApiResponseFactory.CreateFail<bool>("删除任务时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 更新任务状态
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <param name="request">更新状态请求</param>
        /// <returns>操作结果</returns>
        [HttpPatch("{id}/status")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<TaskDto>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<TaskDto>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<TaskDto>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<TaskDto>))]
        public async Task<IActionResult> UpdateTaskStatus(long id, [FromBody] UpdateTaskStatusRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponseFactory.CreateFail<TaskDto>("请求数据无效"));
                }

                var currentUserId = GetCurrentUserId();
                var result = await _taskService.UpdateTaskStatusAsync(id, request.Status, request.Remarks, currentUserId);
                
                if (!result.Success)
                {
                    return result.Error.Contains("未找到") ? NotFound(result) : BadRequest(result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新任务状态时发生错误: ID={TaskId}", id);
                return StatusCode(StatusCodes.Status500InternalServerError, 
                    ApiResponseFactory.CreateFail<TaskDto>("更新任务状态时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 更新任务进度
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <param name="request">更新进度请求</param>
        /// <returns>操作结果</returns>
        [HttpPatch("{id}/progress")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<TaskDto>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<TaskDto>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<TaskDto>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<TaskDto>))]
        public async Task<IActionResult> UpdateTaskProgress(long id, [FromBody] UpdateTaskProgressRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponseFactory.CreateFail<TaskDto>("请求数据无效"));
                }

                var currentUserId = GetCurrentUserId();
                var result = await _taskService.UpdateTaskProgressAsync(id, request.Progress, request.Remarks, currentUserId);
                
                if (!result.Success)
                {
                    return result.Error.Contains("未找到") ? NotFound(result) : BadRequest(result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新任务进度时发生错误: ID={TaskId}", id);
                return StatusCode(StatusCodes.Status500InternalServerError, 
                    ApiResponseFactory.CreateFail<TaskDto>("更新任务进度时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 分配任务
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <param name="request">分配任务请求</param>
        /// <returns>操作结果</returns>
        [HttpPatch("{id}/assign")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<TaskDto>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<TaskDto>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<TaskDto>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<TaskDto>))]
        public async Task<IActionResult> AssignTask(long id, [FromBody] AssignTaskRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponseFactory.CreateFail<TaskDto>("请求数据无效"));
                }

                var currentUserId = GetCurrentUserId();
                var result = await _taskService.AssignTaskAsync(id, request.AssigneeUserId, request.Remarks, currentUserId);
                
                if (!result.Success)
                {
                    return result.Error.Contains("未找到") ? NotFound(result) : BadRequest(result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "分配任务时发生错误: ID={TaskId}", id);
                return StatusCode(StatusCodes.Status500InternalServerError, 
                    ApiResponseFactory.CreateFail<TaskDto>("分配任务时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 完成任务
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <param name="remarks">备注</param>
        /// <returns>操作结果</returns>
        [HttpPatch("{id}/complete")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<TaskDto>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<TaskDto>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<TaskDto>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<TaskDto>))]
        public async Task<IActionResult> CompleteTask(long id, [FromQuery] string remarks = null)
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                var result = await _taskService.CompleteTaskAsync(id, remarks, currentUserId);
                
                if (!result.Success)
                {
                    return result.Error.Contains("未找到") ? NotFound(result) : BadRequest(result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "完成任务时发生错误: ID={TaskId}", id);
                return StatusCode(StatusCodes.Status500InternalServerError, 
                    ApiResponseFactory.CreateFail<TaskDto>("完成任务时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 获取任务评论列表
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>评论列表</returns>
        [HttpGet("{taskId}/comments")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<List<CommentDto>>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<List<CommentDto>>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<List<CommentDto>>))]
        public async Task<IActionResult> GetTaskComments(long taskId)
        {
            try
            {
                var result = await _taskService.GetTaskCommentsAsync(taskId);
                if (!result.Success)
                {
                    return NotFound(result);
                }
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务评论列表时发生错误: TaskId={TaskId}", taskId);
                return StatusCode(StatusCodes.Status500InternalServerError, 
                    ApiResponseFactory.CreateFail<List<CommentDto>>("获取任务评论列表时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 添加任务评论
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="request">评论请求</param>
        /// <returns>添加的评论</returns>
        [HttpPost("{taskId}/comments")]
        [ProducesResponseType(StatusCodes.Status201Created, Type = typeof(ApiResponse<CommentDto>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<CommentDto>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<CommentDto>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<CommentDto>))]
        public async Task<IActionResult> AddTaskComment(long taskId, [FromBody] AddCommentRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponseFactory.CreateFail<CommentDto>("请求数据无效"));
                }

                var currentUserId = GetCurrentUserId();
                var result = await _taskService.AddCommentAsync(taskId, request, currentUserId);
                
                if (!result.Success)
                {
                    return result.Error.Contains("未找到") ? NotFound(result) : BadRequest(result);
                }

                return CreatedAtAction(nameof(GetTaskComments), new { taskId }, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加任务评论时发生错误: TaskId={TaskId}", taskId);
                return StatusCode(StatusCodes.Status500InternalServerError, 
                    ApiResponseFactory.CreateFail<CommentDto>("添加任务评论时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 获取任务附件列表
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>附件列表</returns>
        [HttpGet("{taskId}/attachments")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<List<AttachmentDto>>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<List<AttachmentDto>>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<List<AttachmentDto>>))]
        public async Task<IActionResult> GetTaskAttachments(long taskId)
        {
            try
            {
                var result = await _taskService.GetTaskAttachmentsAsync(taskId);
                if (!result.Success)
                {
                    return NotFound(result);
                }
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务附件列表时发生错误: TaskId={TaskId}", taskId);
                return StatusCode(StatusCodes.Status500InternalServerError, 
                    ApiResponseFactory.CreateFail<List<AttachmentDto>>("获取任务附件列表时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 上传任务附件
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="file">文件</param>
        /// <param name="description">描述</param>
        /// <returns>上传的附件</returns>
        [HttpPost("{taskId}/attachments")]
        [ProducesResponseType(StatusCodes.Status201Created, Type = typeof(ApiResponse<AttachmentDto>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<AttachmentDto>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<AttachmentDto>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<AttachmentDto>))]
        public async Task<IActionResult> UploadTaskAttachment(long taskId, IFormFile file, [FromQuery] string description = null)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return BadRequest(ApiResponseFactory.CreateFail<AttachmentDto>("未提供有效的文件"));
                }

                using var stream = new MemoryStream();
                await file.CopyToAsync(stream);

                var currentUserId = GetCurrentUserId();
                var result = await _taskService.AddAttachmentAsync(taskId, file.FileName, stream.ToArray(), file.ContentType, description, currentUserId);
                
                if (!result.Success)
                {
                    return result.Error.Contains("未找到") ? NotFound(result) : BadRequest(result);
                }

                return CreatedAtAction(nameof(GetTaskAttachments), new { taskId }, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "上传任务附件时发生错误: TaskId={TaskId}", taskId);
                return StatusCode(StatusCodes.Status500InternalServerError, 
                    ApiResponseFactory.CreateFail<AttachmentDto>("上传任务附件时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 获取任务历史记录
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>历史记录列表</returns>
        [HttpGet("{taskId:long}/history")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<List<TaskHistoryDto>>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<List<TaskHistoryDto>>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<List<TaskHistoryDto>>))]
        public async Task<IActionResult> GetTaskHistory(long taskId)
        {
            try
            {
                _logger.LogInformation("获取任务 {TaskId} 的历史记录", taskId);
                var result = await _taskService.GetTaskHistoryAsync(taskId);
                if (!result.Success)
                {
                    _logger.LogWarning("任务 {TaskId} 的历史记录获取失败: {Error}", taskId, result.Error);
                    return NotFound(result);
                }
                _logger.LogInformation("成功获取任务 {TaskId} 的 {Count} 条历史记录", taskId, result.Data?.Count ?? 0);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务历史记录时发生错误: TaskId={TaskId}", taskId);
                return StatusCode(StatusCodes.Status500InternalServerError, 
                    ApiResponseFactory.CreateFail<List<TaskHistoryDto>>("获取任务历史记录时发生服务器错误: " + ex.Message));
            }
        }
    }
} 