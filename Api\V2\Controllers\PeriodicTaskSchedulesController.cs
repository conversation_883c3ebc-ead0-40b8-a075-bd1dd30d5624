// File: Api/V2/PeriodicTaskSchedulesController.cs
// Description: API控制器，用于管理周期性任务计划

using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Application.Features.Tasks.Dtos;
using ItAssetsSystem.Application.Features.Tasks.Services; // Assuming ITaskService or a new IPeriodicTaskScheduleService handles the logic
using ItAssetsSystem.Core.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace ItAssetsSystem.Api.V2.Controllers
{
    /// <summary>
    /// 周期性任务计划管理 API
    /// </summary>
    [ApiController]
    [Route("api/v2/periodic-schedules")]
    [Authorize]
    public class PeriodicTaskSchedulesController : ControllerBase
    {
        private readonly ITaskService _taskService; // Or a dedicated IPeriodicTaskScheduleService
        private readonly ILogger<PeriodicTaskSchedulesController> _logger;

        public PeriodicTaskSchedulesController(ITaskService taskService, ILogger<PeriodicTaskSchedulesController> logger)
        {
            _taskService = taskService ?? throw new ArgumentNullException(nameof(taskService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        private int GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
            if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out var userId))
            {
                // This should ideally not happen if [Authorize] is effective
                // and the token is valid and contains the NameIdentifier claim.
                _logger.LogWarning("无法从JWT声明中获取用户ID。");
                throw new UnauthorizedAccessException("未能获取有效的用户ID。");
            }
            return userId;
        }

        /// <summary>
        /// 获取周期性任务计划列表（分页）
        /// </summary>
        /// <param name="parameters">查询参数</param>
        /// <returns>周期性任务计划列表</returns>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<PaginatedResult<PeriodicTaskScheduleDto>>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<PaginatedResult<PeriodicTaskScheduleDto>>))]
        public async Task<IActionResult> GetPeriodicSchedules([FromQuery] PeriodicTaskScheduleQueryParametersDto parameters)
        {
            try
            {
                // Assuming ITaskService has a method like GetPeriodicSchedulesPagedAsync
                // If not, this method needs to be added to ITaskService and its implementation.
                var result = await _taskService.GetPeriodicSchedulesPagedAsync(parameters);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取周期性任务计划列表时发生错误。");
                return StatusCode(StatusCodes.Status500InternalServerError,
                    ApiResponseFactory.CreateFail<PaginatedResult<PeriodicTaskScheduleDto>>("获取周期性任务计划列表时发生服务器错误：" + ex.Message));
            }
        }

        /// <summary>
        /// 获取指定的周期性任务计划详情
        /// </summary>
        /// <param name="id">计划ID</param>
        /// <returns>周期性任务计划详情</returns>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<PeriodicTaskScheduleDto>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<PeriodicTaskScheduleDto>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<PeriodicTaskScheduleDto>))]
        public async Task<IActionResult> GetPeriodicSchedule(long id)
        {
            try
            {
                var result = await _taskService.GetPeriodicScheduleByIdAsync(id);
                if (!result.Success)
                {
                    return NotFound(result);
                }
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取周期性任务计划详情 (ID={ScheduleId}) 时发生错误。", id);
                return StatusCode(StatusCodes.Status500InternalServerError,
                    ApiResponseFactory.CreateFail<PeriodicTaskScheduleDto>("获取周期性任务计划详情时发生服务器错误：" + ex.Message));
            }
        }

        /// <summary>
        /// 创建新的周期性任务计划
        /// </summary>
        /// <param name="request">创建请求</param>
        /// <returns>创建的周期性任务计划</returns>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created, Type = typeof(ApiResponse<PeriodicTaskScheduleDto>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<PeriodicTaskScheduleDto>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<PeriodicTaskScheduleDto>))]
        public async Task<IActionResult> CreatePeriodicSchedule([FromBody] CreatePeriodicTaskScheduleRequestDto request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponseFactory.CreateFail<PeriodicTaskScheduleDto>(ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage)));
            }
            try
            {
                // 新增：解析周期类型
                var scheduleType = request.RecurrenceType?.ToLower() ?? "daily";
                DateTime nextExecutionTime;

                // 新增：不同周期类型的时间计算
                switch (scheduleType)
                {
                    case "daily":
                        nextExecutionTime = DateTime.Now.AddDays(request.RecurrenceInterval ?? 1);
                        break;
                    case "weekly":
                        // 计算下一个指定周几（如request.WeekDay=1代表周一）
                        int dayOffset = (request.WeekDay ?? 1) - (int)DateTime.Now.DayOfWeek;
                        if (dayOffset <= 0) dayOffset += 7;
                        nextExecutionTime = DateTime.Now.AddDays(dayOffset);
                        break;
                    case "monthly":
                        // 计算下月指定日期（如request.MonthDay=15代表每月15号）
                        int monthDay = request.MonthDay ?? 1;
                        var nextMonth = DateTime.Now.AddMonths(1);
                        nextExecutionTime = new DateTime(
                            nextMonth.Year,
                            nextMonth.Month,
                            Math.Min(monthDay, GetDaysInMonth(nextMonth.Year, nextMonth.Month))
                        );
                        break;
                    case "yearly":
                        // 计算下一年同一月日
                        nextExecutionTime = DateTime.Now.AddYears(request.RecurrenceInterval ?? 1);
                        break;
                    default:
                        return BadRequest(ApiResponseFactory.CreateFail<PeriodicTaskScheduleDto>("不支持的周期类型"));
                }

                var currentUserId = GetCurrentUserId();
                var result = await _taskService.CreatePeriodicScheduleAsync(request, currentUserId);
                if (!result.Success)
                {
                    return BadRequest(result);
                }
                return CreatedAtAction(nameof(GetPeriodicSchedule), new { id = result.Data.Id }, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建周期性任务计划时发生错误。");
                return StatusCode(StatusCodes.Status500InternalServerError,
                    ApiResponseFactory.CreateFail<PeriodicTaskScheduleDto>("创建周期性任务计划时发生服务器错误：" + ex.Message));
            }
        }

        /// <summary>
        /// 新增：获取月份天数工具方法
        /// </summary>
        private int GetDaysInMonth(int year, int month)
        {
            return DateTime.DaysInMonth(year, month);
        }

        /// <summary>
        /// 更新现有的周期性任务计划
        /// </summary>
        /// <param name="id">计划ID</param>
        /// <param name="request">更新请求</param>
        /// <returns>更新后的周期性任务计划</returns>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<PeriodicTaskScheduleDto>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<PeriodicTaskScheduleDto>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<PeriodicTaskScheduleDto>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<PeriodicTaskScheduleDto>))]
        public async Task<IActionResult> UpdatePeriodicSchedule(long id, [FromBody] UpdatePeriodicTaskScheduleRequestDto request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponseFactory.CreateFail<PeriodicTaskScheduleDto>(ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage)));
            }
            try
            {
                var currentUserId = GetCurrentUserId();
                var result = await _taskService.UpdatePeriodicScheduleAsync(id, request, currentUserId);
                if (!result.Success)
                {
                    return result.Error != null && result.Error.Contains("未找到") ? NotFound(result) : BadRequest(result);
                }
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新周期性任务计划 (ID={ScheduleId}) 时发生错误。", id);
                return StatusCode(StatusCodes.Status500InternalServerError,
                    ApiResponseFactory.CreateFail<PeriodicTaskScheduleDto>("更新周期性任务计划时发生服务器错误：" + ex.Message));
            }
        }

        /// <summary>
        /// 删除周期性任务计划（软删除）
        /// </summary>
        /// <param name="id">计划ID</param>
        /// <returns>操作结果</returns>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<bool>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<bool>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<bool>))]
        public async Task<IActionResult> DeletePeriodicSchedule(long id)
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                var result = await _taskService.DeletePeriodicScheduleAsync(id, currentUserId);
                if (!result.Success)
                {
                     return result.Error != null && result.Error.Contains("未找到") ? NotFound(result) : BadRequest(result);
                }
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除周期性任务计划 (ID={ScheduleId}) 时发生错误。", id);
                return StatusCode(StatusCodes.Status500InternalServerError,
                    ApiResponseFactory.CreateFail<bool>("删除周期性任务计划时发生服务器错误：" + ex.Message));
            }
        }

        /// <summary>
        /// 启用或禁用周期性任务计划
        /// </summary>
        /// <param name="id">计划ID</param>
        /// <param name="isEnabled">是否启用</param>
        /// <returns>操作结果</returns>
        [HttpPatch("{id}/enable")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<bool>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<bool>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<bool>))]
        public async Task<IActionResult> EnablePeriodicSchedule(long id, [FromQuery] bool isEnabled)
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                // Assuming a method in ITaskService to handle this
                var result = await _taskService.EnablePeriodicScheduleAsync(id, isEnabled, currentUserId);
                if (!result.Success)
                {
                    return result.Error != null && result.Error.Contains("未找到") ? NotFound(result) : BadRequest(result);
                }
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "启用/禁用周期性任务计划 (ID={ScheduleId}, IsEnabled={IsEnabled}) 时发生错误。", id, isEnabled);
                return StatusCode(StatusCodes.Status500InternalServerError,
                    ApiResponseFactory.CreateFail<bool>("启用/禁用周期性任务计划时发生服务器错误：" + ex.Message));
            }
        }
    }
} 